package trader

/*
交易员,主要用于执行交易策略,管理订单,监控风险,计算盈亏
*/

import (
	"crypto-arbitrage-trader/internal/exchange"
	"crypto-arbitrage-trader/internal/grid"
	"crypto-arbitrage-trader/internal/hedge"
	"crypto-arbitrage-trader/internal/market"
	"crypto-arbitrage-trader/internal/order"
	"crypto-arbitrage-trader/internal/risk"
	"crypto-arbitrage-trader/pkg/config"
	"crypto-arbitrage-trader/pkg/types"
	"crypto-arbitrage-trader/pkg/utils/log"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
)

/*
交易员
*/
type ArbitrageTrader struct {
	config          *config.Config
	exchange        exchange.Exchange
	marketAnalyzer  *market.Analyzer
	gridCalculator  *grid.Calculator
	hedgeCalculator *hedge.Calculator
	riskManager     *risk.Manager
	orderManager    *order.Manager

	currentSession *types.TradingSession
	stopChan       chan struct{}
	dryRun         bool
}

func New(cfg *config.Config, dryRun bool) (*ArbitrageTrader, error) {
	// Initialize exchange
	factory := exchange.NewExchangeFactory()
	ex, err := factory.CreateExchange(cfg.Exchange.Name, cfg.GetExchangeConfig())
	if err != nil {
		return nil, fmt.Errorf("failed to create exchange: %w", err)
	}

	// Connect to exchange
	if !dryRun {
		if err := ex.Connect(); err != nil {
			return nil, fmt.Errorf("failed to connect to exchange: %w", err)
		}
	}

	trader := &ArbitrageTrader{
		config:          cfg,
		exchange:        ex,
		marketAnalyzer:  market.NewAnalyzer(),
		gridCalculator:  grid.NewCalculator(),
		hedgeCalculator: hedge.NewCalculator(),
		riskManager:     risk.NewManager(),
		orderManager:    order.NewManager(ex),
		stopChan:        make(chan struct{}),
		dryRun:          dryRun,
	}

	return trader, nil
}

func (at *ArbitrageTrader) Start() error {
	// 启动订单管理器
	if err := at.orderManager.Start(); err != nil {
		return fmt.Errorf("failed to start order manager: %w", err)
	}

	// 启动交易会话
	at.currentSession = &types.TradingSession{
		ID:             fmt.Sprintf("session_%d", time.Now().Unix()),
		Symbol:         at.config.Trading.Symbol,
		StartTime:      time.Now(),
		Status:         "active",
		InitialBalance: at.config.Trading.Investment,
		CurrentBalance: at.config.Trading.Investment,
		DailyTarget:    at.config.Trading.ExpectedDailyReturn,
	}

	// 启动主交易循环
	go at.tradingLoop()

	return nil
}

func (at *ArbitrageTrader) Stop() error {
	close(at.stopChan)

	// 取消所有订单
	if err := at.orderManager.CancelAllOrders(at.config.Trading.Symbol); err != nil {
		log.WithError(err).Error("Failed to cancel orders during shutdown")
	}

	// 停止订单管理器
	if err := at.orderManager.Stop(); err != nil {
		log.WithError(err).Error("Failed to stop order manager")
	}

	// 断开与交易所的连接
	if err := at.exchange.Disconnect(); err != nil {
		log.WithError(err).Error("Failed to disconnect from exchange")
	}

	// 更新会话状态
	if at.currentSession != nil {
		now := time.Now()
		at.currentSession.EndTime = &now
		at.currentSession.Status = "stopped"
		at.logSessionSummary()
	}

	return nil
}

func (at *ArbitrageTrader) tradingLoop() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	// 初始设置
	if err := at.setupTradingStrategy(); err != nil {
		log.WithError(err).Error("Failed to setup trading strategy")
		return
	}

	for {
		select {
		case <-at.stopChan:
			return
		case <-ticker.C:
			at.processTradingCycle()
		}
	}
}

func (at *ArbitrageTrader) setupTradingStrategy() error {
	symbol := at.config.Trading.Symbol

	log.Infof("Setting up trading strategy for %s", symbol)

	// 1. 获取市场数据
	market, err := at.exchange.GetTicker(symbol)
	if err != nil {
		return fmt.Errorf("failed to get market data: %w", err)
	}

	log.Infof("Current market price: %s", market.CurrentPrice.String())

	// 2. 分析市场并获取价格范围建议
	priceRange, err := at.marketAnalyzer.AnalyzeMarket(market)
	if err != nil {
		return fmt.Errorf("failed to analyze market: %w", err)
	}

	log.Infof("Suggested price range: %s - %s (confidence: %.2f%%)",
		priceRange.LowerBound.String(), priceRange.UpperBound.String(), priceRange.Confidence*100)

	// 3. 计算网格参数
	gridParam, err := at.gridCalculator.CalculateGridParameters(
		symbol,
		at.config.Trading.Investment,
		at.config.Trading.ExpectedDailyReturn,
		*priceRange,
	)
	if err != nil {
		return fmt.Errorf("failed to calculate grid parameters: %w", err)
	}

	log.Infof("Grid configuration: %d grids, spacing: %s, order size: %s",
		gridParam.GridNum, gridParam.GridSpacing.String(), gridParam.OrderSize.String())

	// 4. 计算对冲仓位
	volatility := market.PriceChange.Abs().Div(market.CurrentPrice).Mul(decimal.NewFromInt(100))
	hedgePosition, err := at.hedgeCalculator.CalculateHedgePosition(gridParam, market.CurrentPrice, volatility)
	if err != nil {
		return fmt.Errorf("failed to calculate hedge position: %w", err)
	}

	log.Infof("Hedge position: %s %s, ratio: %s, collateral: %s",
		hedgePosition.Direction, hedgePosition.Size.String(),
		hedgePosition.HedgeRatio.String(), hedgePosition.CollateralReq.String())

	// 5. Calculate risk parameters
	riskParams, err := at.riskManager.CalculateRiskParameters(gridParam, hedgePosition, market.CurrentPrice)
	if err != nil {
		return fmt.Errorf("failed to calculate risk parameters: %w", err)
	}

	log.Infof("Risk parameters - Stop Loss: %s, Take Profit: %s",
		riskParams.StopLoss.String(), riskParams.TakeProfit.String())

	// 6. Generate grid levels
	buyLevels, sellLevels, err := at.gridCalculator.GenerateGridLevels(gridParam)
	if err != nil {
		return fmt.Errorf("failed to generate grid levels: %w", err)
	}

	log.Infof("Generated %d buy levels and %d sell levels", len(buyLevels), len(sellLevels))

	// Skip actual trading in dry run mode
	if at.dryRun {
		log.Info("Dry run mode: skipping actual order placement")
		log.Info("Trading strategy setup completed successfully (dry run)")
		return nil
	}

	// 7. Create grid orders
	if err := at.orderManager.CreateGridOrders(gridParam, buyLevels, sellLevels); err != nil {
		return fmt.Errorf("failed to create grid orders: %w", err)
	}

	// 8. Create hedge position
	if err := at.orderManager.CreateHedgePosition(symbol, hedgePosition); err != nil {
		log.WithError(err).Warn("Failed to create hedge position, continuing without hedge")
	}

	log.Info("Trading strategy setup completed successfully")
	return nil
}

func (at *ArbitrageTrader) processTradingCycle() {
	symbol := at.config.Trading.Symbol

	// Skip position updates in dry run mode
	if at.dryRun {
		log.Debug("Dry run mode: skipping position updates")
		return
	}

	// Update positions
	if err := at.orderManager.UpdatePositions(); err != nil {
		log.WithError(err).Debug("Failed to update positions (expected for spot trading)")
	}

	// Calculate current PnL
	realizedPnL := at.orderManager.CalculateRealizedPnL(symbol)
	at.currentSession.RealizedPnL = realizedPnL
	at.currentSession.CurrentBalance = at.currentSession.InitialBalance.Add(realizedPnL)

	// Check risk conditions
	riskParams := &types.RiskManagement{
		StopLoss:    at.config.Trading.Investment.Mul(at.config.Risk.MaxDailyLoss),
		TakeProfit:  at.config.Trading.ExpectedDailyReturn,
		MaxDrawdown: at.config.Trading.Investment.Mul(at.config.Risk.MaxDrawdown),
		DailyTarget: at.config.Trading.ExpectedDailyReturn,
	}

	positions, _ := at.orderManager.GetPosition(symbol)
	var positionSlice []types.Position
	if positions != nil {
		positionSlice = []types.Position{*positions}
	}

	riskStatus, err := at.riskManager.MonitorRisk(at.currentSession, positionSlice, riskParams)
	if err != nil {
		log.WithError(err).Error("Failed to monitor risk")
		return
	}

	// Log current status
	log.Infof("Session PnL: %s, Risk Level: %s",
		realizedPnL.String(), riskStatus.RiskLevel)

	// Check for alerts
	for _, alert := range riskStatus.Alerts {
		log.Warn(alert)
	}

	// Check if we should stop
	if riskStatus.ShouldStop {
		log.Infof("Stopping trading: %s", riskStatus.StopReason)
		at.currentSession.StopReason = riskStatus.StopReason
		go at.Stop()
		return
	}

	// Check emergency stop
	if shouldStop, reason := at.riskManager.CheckEmergencyStop(at.currentSession); shouldStop {
		log.Errorf("Emergency stop triggered: %s", reason)
		at.currentSession.StopReason = reason
		go at.Stop()
		return
	}

	// Log order summary
	summary := at.orderManager.GetOrderSummary(symbol)
	log.Debugf("Order summary: %+v", summary)
}

func (at *ArbitrageTrader) logSessionSummary() {
	if at.currentSession == nil {
		return
	}

	duration := time.Since(at.currentSession.StartTime)
	finalPnL := at.currentSession.CurrentBalance.Sub(at.currentSession.InitialBalance)
	returnRate := finalPnL.Div(at.currentSession.InitialBalance).Mul(decimal.NewFromInt(100))

	log.Info("=== Trading Session Summary ===")
	log.Infof("Session ID: %s", at.currentSession.ID)
	log.Infof("Symbol: %s", at.currentSession.Symbol)
	log.Infof("Duration: %s", duration.String())
	log.Infof("Initial Balance: %s", at.currentSession.InitialBalance.String())
	log.Infof("Final Balance: %s", at.currentSession.CurrentBalance.String())
	log.Infof("Realized PnL: %s", finalPnL.String())
	log.Infof("Return Rate: %s%%", returnRate.String())
	log.Infof("Target Return: %s", at.currentSession.DailyTarget.String())
	log.Infof("Stop Reason: %s", at.currentSession.StopReason)
	log.Infof("Status: %s", at.currentSession.Status)

	// Order statistics
	if !at.dryRun {
		summary := at.orderManager.GetOrderSummary(at.currentSession.Symbol)
		log.Infof("Order Summary: %+v", summary)
	}

	log.Info("================================")
}

func (at *ArbitrageTrader) GetConfig() *config.Config {
	return at.config
}
