package grid

import (
	"crypto-arbitrage-trader/pkg/types"
	"testing"

	"github.com/shopspring/decimal"
)

func TestCalculateProfitPerGrid(t *testing.T) {
	calc := NewCalculator()

	// 测试用例
	testCases := []struct {
		name           string
		investment     decimal.Decimal
		expectedReturn decimal.Decimal
		priceRange     types.PriceRange
		gridLevels     int
		orderSize      decimal.Decimal
		expectedMin    decimal.Decimal
		expectedMax    decimal.Decimal
	}{
		{
			name:           "BTC 1000 USDT 投资",
			investment:     decimal.NewFromInt(1000),
			expectedReturn: decimal.NewFromInt(20),
			priceRange: types.PriceRange{
				LowerBound: decimal.NewFromFloat(45000),
				UpperBound: decimal.NewFromFloat(55000),
			},
			gridLevels:  10,
			orderSize:   decimal.NewFromInt(70), // 1000 * 0.7 / 10
			expectedMin: decimal.NewFromFloat(0.5),
			expectedMax: decimal.NewFromFloat(5.0),
		},
		{
			name:           "ETH 500 USDT 投资",
			investment:     decimal.NewFromInt(500),
			expectedReturn: decimal.NewFromInt(10),
			priceRange: types.PriceRange{
				LowerBound: decimal.NewFromFloat(3000),
				UpperBound: decimal.NewFromFloat(3500),
			},
			gridLevels:  8,
			orderSize:   decimal.NewFromInt(44), // 500 * 0.7 / 8
			expectedMin: decimal.NewFromFloat(0.3),
			expectedMax: decimal.NewFromFloat(3.0),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			profitPerGrid := calc.calculateProfitPerGrid(
				tc.investment,
				tc.expectedReturn,
				tc.priceRange,
				tc.gridLevels,
				tc.orderSize,
			)

			// 验证每网格利润在合理范围内
			if profitPerGrid.LessThan(tc.expectedMin) {
				t.Errorf("每网格利润 %s 太小，期望至少 %s", profitPerGrid.String(), tc.expectedMin.String())
			}

			if profitPerGrid.GreaterThan(tc.expectedMax) {
				t.Errorf("每网格利润 %s 太大，期望最多 %s", profitPerGrid.String(), tc.expectedMax.String())
			}

			// 验证总利润不超过预期
			totalProfit := profitPerGrid.Mul(decimal.NewFromInt(int64(tc.gridLevels)))
			if totalProfit.GreaterThan(tc.expectedReturn.Mul(decimal.NewFromFloat(1.2))) {
				t.Errorf("总利润 %s 超过预期 %s 的120%%", totalProfit.String(), tc.expectedReturn.String())
			}

			t.Logf("每网格利润: %s, 总利润: %s", profitPerGrid.String(), totalProfit.String())
		})
	}
}

func TestCalculateGridSpacing(t *testing.T) {
	calc := NewCalculator()

	priceRange := types.PriceRange{
		LowerBound: decimal.NewFromFloat(45000),
		UpperBound: decimal.NewFromFloat(55000),
	}

	gridLevels := 10
	spacing := calc.calculateGridSpacing(priceRange, gridLevels)

	// 验证网格间距
	expectedSpacing := decimal.NewFromFloat(1111.11) // (55000-45000) / 9
	tolerance := decimal.NewFromFloat(100)

	if spacing.Sub(expectedSpacing).Abs().GreaterThan(tolerance) {
		t.Errorf("网格间距 %s 与期望 %s 差异太大", spacing.String(), expectedSpacing.String())
	}

	t.Logf("网格间距: %s", spacing.String())
}

func TestCalculateOrderSize(t *testing.T) {
	calc := NewCalculator()

	investment := decimal.NewFromInt(1000)
	gridLevels := 10

	orderSize := calc.calculateOrderSize(investment, gridLevels)

	// 验证订单大小
	expectedOrderSize := decimal.NewFromInt(70) // 1000 * 0.7 / 10
	tolerance := decimal.NewFromFloat(5)

	if orderSize.Sub(expectedOrderSize).Abs().GreaterThan(tolerance) {
		t.Errorf("订单大小 %s 与期望 %s 差异太大", orderSize.String(), expectedOrderSize.String())
	}

	// 验证最小订单大小
	minOrderSize := decimal.NewFromInt(10)
	if orderSize.LessThan(minOrderSize) {
		t.Errorf("订单大小 %s 小于最小要求 %s", orderSize.String(), minOrderSize.String())
	}

	t.Logf("订单大小: %s", orderSize.String())
}
