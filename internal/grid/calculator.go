package grid

import (
	"crypto-arbitrage-trader/pkg/types"
	"fmt"
	"math"

	"github.com/shopspring/decimal"
)

type Calculator struct {
	minGridNum    int
	maxGridNum    int
	minProfitRate decimal.Decimal
}

func NewCalculator() *Calculator {
	return &Calculator{
		minGridNum:    5,
		maxGridNum:    50,
		minProfitRate: decimal.NewFromFloat(0.001), // 0.1% minimum profit per grid
	}
}

// 入口函数
// 计算网格交易参数
func (c *Calculator) CalculateGridParameters(
	symbol string,
	investment decimal.Decimal,
	expectedReturn decimal.Decimal,
	priceRange types.PriceRange,
) (*types.GridParam, error) {

	if investment.LessThanOrEqual(decimal.Zero) {
		return nil, fmt.Errorf("investment amount must be positive")
	}

	if expectedReturn.LessThanOrEqual(decimal.Zero) {
		return nil, fmt.Errorf("expected return must be positive")
	}

	// 计算最佳网格数量
	gridNum, err := c.calculateOptimalGridNum(investment, expectedReturn, priceRange)
	if err != nil {
		return nil, err
	}

	// 计算网格间距
	gridSpacing := c.calculateGridSpacing(priceRange, gridNum)

	// 计算每网格订单大小
	orderSize := c.calculateOrderSize(investment, gridNum)

	// 计算每网格利润
	profitPerGrid := c.calculateProfitPerGrid(investment, expectedReturn, priceRange, gridNum, orderSize)

	gridConfig := &types.GridParam{
		Symbol:         symbol,
		Investment:     investment,
		ExpectedReturn: expectedReturn,
		PriceRange:     priceRange,
		GridNum:        gridNum,
		GridSpacing:    gridSpacing,
		OrderSize:      orderSize,
		ProfitPerGrid:  profitPerGrid,
	}

	// 验证配置
	if err := c.validateGridConfig(gridConfig); err != nil {
		return nil, err
	}

	return gridConfig, nil
}

// 计算最佳网格数量
func (c *Calculator) calculateOptimalGridNum(
	investment decimal.Decimal,
	expectedReturn decimal.Decimal,
	priceRange types.PriceRange,
) (int, error) {

	// 计算价格范围百分比
	rangeSize := priceRange.UpperBound.Sub(priceRange.LowerBound)
	midPrice := priceRange.LowerBound.Add(priceRange.UpperBound).Div(decimal.NewFromInt(2))
	rangePercent := rangeSize.Div(midPrice)

	// 计算每网格所需利润率
	returnRate := expectedReturn.Div(investment)

	// 根据范围和目标回报估计最佳网格数量
	// 更宽的范围需要更多网格, 更高的目标回报需要更少网格
	rangePercentFloat, _ := rangePercent.Float64()
	returnRateFloat, _ := returnRate.Float64()

	// 基础计算: 每网格至少0.2%的利润
	targetProfitPerGrid := math.Max(0.002, returnRateFloat/20)

	// 计算所需网格数量
	estimatedNum := int(rangePercentFloat / (targetProfitPerGrid * 2))

	// 应用约束
	if estimatedNum < c.minGridNum {
		estimatedNum = c.minGridNum
	}
	if estimatedNum > c.maxGridNum {
		estimatedNum = c.maxGridNum
	}

	return estimatedNum, nil
}

// 计算网格间距
func (c *Calculator) calculateGridSpacing(priceRange types.PriceRange, gridNum int) decimal.Decimal {
	// 计算价格范围
	rangeSize := priceRange.UpperBound.Sub(priceRange.LowerBound)

	// 网格间距 = 价格范围 / (网格数量 - 1)
	// 减1是因为网格数量包括边界点
	spacing := rangeSize.Div(decimal.NewFromInt(int64(gridNum - 1)))

	// 确保最小间距 (0.1%)
	midPrice := priceRange.LowerBound.Add(priceRange.UpperBound).Div(decimal.NewFromInt(2))
	minSpacing := midPrice.Mul(decimal.NewFromFloat(0.001))

	if spacing.LessThan(minSpacing) {
		spacing = minSpacing
	}

	return spacing
}

// 计算每网格订单大小
func (c *Calculator) calculateOrderSize(investment decimal.Decimal, gridNum int) decimal.Decimal {
	// 为对冲仓位保留一些资本 (通常为30%)
	tradingCapital := investment.Mul(decimal.NewFromFloat(0.7))

	// 将资本分配给网格数量, 但不要相等 - 使用更多资本用于中间网格
	// 为了简化, 这里使用相等分配
	return tradingCapital.Div(decimal.NewFromInt(int64(gridNum)))
}

// 计算每网格预期利润
func (c *Calculator) calculateProfitPerGrid(
	investment decimal.Decimal,
	expectedReturn decimal.Decimal,
	priceRange types.PriceRange,
	gridNum int,
	orderSize decimal.Decimal,
) decimal.Decimal {

	// 计算价格范围
	rangeSize := priceRange.UpperBound.Sub(priceRange.LowerBound)
	midPrice := priceRange.LowerBound.Add(priceRange.UpperBound).Div(decimal.NewFromInt(2))

	// 计算网格间距
	gridSpacing := rangeSize.Div(decimal.NewFromInt(int64(gridNum - 1)))

	// 计算每网格的利润率 (基于网格间距)
	// 每网格利润 = 订单大小 × 网格间距 × 利润率系数
	profitRatePerGrid := gridSpacing.Div(midPrice)

	// 考虑交易费用 (假设0.1% 的买卖费用)
	tradingFee := decimal.NewFromFloat(0.001)
	netProfitRate := profitRatePerGrid.Sub(tradingFee.Mul(decimal.NewFromInt(2)))

	// 确保最小利润率
	if netProfitRate.LessThan(c.minProfitRate) {
		netProfitRate = c.minProfitRate
	}

	// 计算每网格利润
	profitPerGrid := orderSize.Mul(netProfitRate)

	// 验证总利润是否符合预期
	totalEstimatedProfit := profitPerGrid.Mul(decimal.NewFromInt(int64(gridNum)))

	// 如果总利润过高，调整每网格利润
	if totalEstimatedProfit.GreaterThan(expectedReturn) {
		profitPerGrid = expectedReturn.Div(decimal.NewFromInt(int64(gridNum)))
	}

	return profitPerGrid
}

// 验证网格配置
func (c *Calculator) validateGridConfig(config *types.GridParam) error {
	// 检查每网格最小利润率
	profitRate := config.ProfitPerGrid.Div(config.OrderSize)
	if profitRate.LessThan(c.minProfitRate) {
		return fmt.Errorf("profit rate per grid (%s%%) is below minimum (%s%%)",
			profitRate.Mul(decimal.NewFromInt(100)).String(),
			c.minProfitRate.Mul(decimal.NewFromInt(100)).String())
	}

	// 检查网格间距是否合理
	midPrice := config.PriceRange.LowerBound.Add(config.PriceRange.UpperBound).Div(decimal.NewFromInt(2))
	spacingPercent := config.GridSpacing.Div(midPrice)

	minSpacing := decimal.NewFromFloat(0.001) // 0.1%
	maxSpacing := decimal.NewFromFloat(0.05)  // 5%

	if spacingPercent.LessThan(minSpacing) {
		return fmt.Errorf("grid spacing (%s%%) is too small, minimum %s%%",
			spacingPercent.Mul(decimal.NewFromInt(100)).String(),
			minSpacing.Mul(decimal.NewFromInt(100)).String())
	}

	if spacingPercent.GreaterThan(maxSpacing) {
		return fmt.Errorf("grid spacing (%s%%) is too large, maximum %s%%",
			spacingPercent.Mul(decimal.NewFromInt(100)).String(),
			maxSpacing.Mul(decimal.NewFromInt(100)).String())
	}

	return nil
}

// 生成所有买入和卖出价格水平
func (c *Calculator) GenerateGridLevels(config *types.GridParam) ([]decimal.Decimal, []decimal.Decimal, error) {
	if config == nil {
		return nil, nil, fmt.Errorf("grid config is required")
	}

	buyLevels := make([]decimal.Decimal, 0)
	sellLevels := make([]decimal.Decimal, 0)

	currentPrice := config.PriceRange.LowerBound.Add(config.PriceRange.UpperBound).Div(decimal.NewFromInt(2))

	// 生成低于当前价格的价格水平 (买入订单)
	for i := 0; i < config.GridNum/2; i++ {
		level := currentPrice.Sub(config.GridSpacing.Mul(decimal.NewFromInt(int64(i + 1))))
		if level.GreaterThanOrEqual(config.PriceRange.LowerBound) {
			buyLevels = append(buyLevels, level)
		}
	}

	// 生成高于当前价格的价格水平 (卖出订单)
	for i := 0; i < config.GridNum/2; i++ {
		level := currentPrice.Add(config.GridSpacing.Mul(decimal.NewFromInt(int64(i + 1))))
		if level.LessThanOrEqual(config.PriceRange.UpperBound) {
			sellLevels = append(sellLevels, level)
		}
	}

	return buyLevels, sellLevels, nil
}

// 估计网格策略的性能
func (c *Calculator) EstimatePerformance(config *types.GridParam) (map[string]interface{}, error) {
	if config == nil {
		return nil, fmt.Errorf("grid config is required")
	}

	midPrice := config.PriceRange.LowerBound.Add(config.PriceRange.UpperBound).Div(decimal.NewFromInt(2))

	// 计算指标
	totalInvestment := config.Investment
	expectedReturn := config.ExpectedReturn
	returnRate := expectedReturn.Div(totalInvestment).Mul(decimal.NewFromInt(100))

	// 根据网格间距估计执行概率
	spacingPercent := config.GridSpacing.Div(midPrice).Mul(decimal.NewFromInt(100))

	// 粗略估计: 更紧密的网格有更高的执行概率
	executionProb := decimal.NewFromFloat(0.8)
	if spacingPercent.LessThan(decimal.NewFromFloat(0.5)) {
		executionProb = decimal.NewFromFloat(0.9)
	} else if spacingPercent.GreaterThan(decimal.NewFromFloat(2)) {
		executionProb = decimal.NewFromFloat(0.6)
	}

	adjustedReturn := expectedReturn.Mul(executionProb)

	performance := map[string]interface{}{
		"total_investment":      totalInvestment,
		"expected_return":       expectedReturn,
		"return_rate":           returnRate,
		"adjusted_return":       adjustedReturn,
		"execution_probability": executionProb,
		"grid_num":              config.GridNum,
		"grid_spacing_pct":      spacingPercent,
		"profit_per_grid":       config.ProfitPerGrid,
		"order_size":            config.OrderSize,
	}

	return performance, nil
}
