package hedge

/*
对冲计算器,主要用于计算对冲比例,对冲大小,对冲方向,对冲入场价格,对冲保证金要求
*/

import (
	"crypto-arbitrage-trader/pkg/types"
	"fmt"

	"github.com/shopspring/decimal"
)

type Calculator struct {
	defaultHedgeRatio decimal.Decimal
	maxHedgeRatio     decimal.Decimal
	minCollateralReq  decimal.Decimal
}

func NewCalculator() *Calculator {
	return &Calculator{
		defaultHedgeRatio: decimal.NewFromFloat(0.5), // 50% hedge ratio
		maxHedgeRatio:     decimal.NewFromFloat(0.8), // 80% max hedge
		minCollateralReq:  decimal.NewFromFloat(0.1), // 10% minimum collateral
	}
}

// 计算网格交易的最佳对冲位置
func (c *Calculator) CalculateHedgePosition(
	gridConfig *types.GridParam,
	currentPrice decimal.Decimal,
	volatility decimal.Decimal,
) (*types.HedgePosition, error) {

	if gridConfig == nil {
		return nil, fmt.Errorf("grid configuration is required")
	}

	if currentPrice.LessThanOrEqual(decimal.Zero) {
		return nil, fmt.Errorf("current price must be positive")
	}

	// 计算对冲比例
	hedgeRatio := c.calculateHedgeRatio(gridConfig, volatility)
	// 计算对冲大小
	hedgeSize := c.calculateHedgeSize(gridConfig, hedgeRatio)
	// 计算对冲方向
	hedgeDirection := c.determineHedgeDirection(gridConfig, currentPrice)
	// 计算对冲入场价格
	entryPrice := c.calculateOptimalEntryPrice(currentPrice, hedgeDirection)
	// 计算对冲保证金要求
	collateralReq := c.calculateCollateralRequirement(hedgeSize, entryPrice, hedgeRatio)

	hedgePosition := &types.HedgePosition{
		Direction:     hedgeDirection,
		Size:          hedgeSize,
		EntryPrice:    entryPrice,
		HedgeRatio:    hedgeRatio,
		CollateralReq: collateralReq,
	}

	return hedgePosition, nil
}

// calculateHedgeRatio determines the optimal hedge ratio based on market conditions
// 计算对冲比例
func (c *Calculator) calculateHedgeRatio(gridConfig *types.GridParam, volatility decimal.Decimal) decimal.Decimal {
	// Base hedge ratio
	hedgeRatio := c.defaultHedgeRatio

	// Adjust based on volatility
	volFloat, _ := volatility.Float64()

	// Higher volatility = higher hedge ratio
	if volFloat > 15.0 { // High volatility (>15%)
		hedgeRatio = decimal.NewFromFloat(0.7)
	} else if volFloat > 10.0 { // Medium volatility (10-15%)
		hedgeRatio = decimal.NewFromFloat(0.6)
	} else if volFloat < 5.0 { // Low volatility (<5%)
		hedgeRatio = decimal.NewFromFloat(0.3)
	}

	// Adjust based on price range width
	rangeWidth := gridConfig.PriceRange.UpperBound.Sub(gridConfig.PriceRange.LowerBound)
	midPrice := gridConfig.PriceRange.LowerBound.Add(gridConfig.PriceRange.UpperBound).Div(decimal.NewFromInt(2))
	rangePercent := rangeWidth.Div(midPrice)

	rangePercentFloat, _ := rangePercent.Float64()

	// Wider range = higher hedge ratio
	if rangePercentFloat > 0.1 { // >10% range
		hedgeRatio = hedgeRatio.Add(decimal.NewFromFloat(0.1))
	} else if rangePercentFloat < 0.02 { // <2% range
		hedgeRatio = hedgeRatio.Sub(decimal.NewFromFloat(0.1))
	}

	// Apply constraints
	if hedgeRatio.GreaterThan(c.maxHedgeRatio) {
		hedgeRatio = c.maxHedgeRatio
	}
	if hedgeRatio.LessThan(decimal.NewFromFloat(0.2)) {
		hedgeRatio = decimal.NewFromFloat(0.2) // Minimum 20%
	}

	return hedgeRatio
}

// calculateHedgeSize calculates the size of the hedge position
func (c *Calculator) calculateHedgeSize(gridConfig *types.GridParam, hedgeRatio decimal.Decimal) decimal.Decimal {
	// Total exposure from grid trading
	totalGridExposure := gridConfig.OrderSize.Mul(decimal.NewFromInt(int64(gridConfig.GridNum)))

	// Calculate hedge size based on hedge ratio
	hedgeSize := totalGridExposure.Mul(hedgeRatio)

	return hedgeSize
}

// determineHedgeDirection determines whether to hedge with long or short position
func (c *Calculator) determineHedgeDirection(gridConfig *types.GridParam, currentPrice decimal.Decimal) string {
	midPrice := gridConfig.PriceRange.LowerBound.Add(gridConfig.PriceRange.UpperBound).Div(decimal.NewFromInt(2))

	// If current price is above mid-range, we expect more buying in grids
	// So hedge with short position to offset upward exposure
	if currentPrice.GreaterThan(midPrice) {
		return "short"
	}

	// If current price is below mid-range, we expect more selling in grids
	// So hedge with long position to offset downward exposure
	return "long"
}

// calculateOptimalEntryPrice calculates the optimal entry price for hedge position
func (c *Calculator) calculateOptimalEntryPrice(currentPrice decimal.Decimal, direction string) decimal.Decimal {
	// For now, use current price with small adjustment for better execution
	adjustment := currentPrice.Mul(decimal.NewFromFloat(0.001)) // 0.1% adjustment

	if direction == "long" {
		// For long hedge, enter slightly below current price
		return currentPrice.Sub(adjustment)
	} else {
		// For short hedge, enter slightly above current price
		return currentPrice.Add(adjustment)
	}
}

// 计算对冲保证金要求
func (c *Calculator) calculateCollateralRequirement(
	hedgeSize, entryPrice, hedgeRatio decimal.Decimal,
) decimal.Decimal {
	// 对冲仓位价值
	positionValue := hedgeSize.Mul(entryPrice)

	// 基础保证金要求 (通常为10-20% 用于加密货币期货)
	baseCollateralRate := decimal.NewFromFloat(0.15) // 15%

	// 根据对冲比例调整保证金要求 - 对冲比例越高可能需要更多保证金
	hedgeRatioFloat, _ := hedgeRatio.Float64()
	collateralMultiplier := decimal.NewFromFloat(1.0 + hedgeRatioFloat*0.2) // 最高20%的增加

	collateralReq := positionValue.Mul(baseCollateralRate).Mul(collateralMultiplier)
	// 确保最低保证金要求
	minCollateral := positionValue.Mul(c.minCollateralReq)
	if collateralReq.LessThan(minCollateral) {
		collateralReq = minCollateral
	}

	return collateralReq
}

// 计算动态对冲调整
func (c *Calculator) CalculateDynamicHedge(
	currentHedge *types.HedgePosition,
	gridPositions []types.Position,
	currentPrice decimal.Decimal,
) (*types.HedgePosition, error) {

	if currentHedge == nil {
		return nil, fmt.Errorf("current hedge position is required")
	}

	// 计算当前网格仓位净暴露, 即多头和空头的差值
	netExposure := decimal.Zero
	for _, pos := range gridPositions {
		if pos.Side == "long" {
			netExposure = netExposure.Add(pos.Size)
		} else {
			netExposure = netExposure.Sub(pos.Size)
		}
	}

	// 计算所需对冲调整
	requiredHedgeSize := netExposure.Mul(currentHedge.HedgeRatio).Abs()

	// 确定是否需要调整
	sizeDifference := requiredHedgeSize.Sub(currentHedge.Size).Abs()
	adjustmentThreshold := currentHedge.Size.Mul(decimal.NewFromFloat(0.1)) // 10% threshold

	if sizeDifference.LessThan(adjustmentThreshold) {
		// 不需要显著调整
		return currentHedge, nil
	}

	// 创建调整后的对冲仓位
	adjustedHedge := &types.HedgePosition{
		Direction:     c.determineAdjustedDirection(netExposure),
		Size:          requiredHedgeSize,
		EntryPrice:    currentPrice,
		HedgeRatio:    currentHedge.HedgeRatio,
		CollateralReq: c.calculateCollateralRequirement(requiredHedgeSize, currentPrice, currentHedge.HedgeRatio),
	}

	return adjustedHedge, nil
}

// 确定对冲方向
func (c *Calculator) determineAdjustedDirection(netExposure decimal.Decimal) string {
	if netExposure.GreaterThan(decimal.Zero) {
		return "short" // 净多头仓位需要做空对冲
	}
	return "long" // 净空头仓位需要做多对冲
}

// 验证对冲仓位
func (c *Calculator) ValidateHedgePosition(hedge *types.HedgePosition, maxInvestment decimal.Decimal) error {
	if hedge == nil {
		return fmt.Errorf("hedge position is required")
	}

	// 检查保证金要求是否合理
	if hedge.CollateralReq.GreaterThan(maxInvestment.Mul(decimal.NewFromFloat(0.5))) {
		return fmt.Errorf("hedge collateral requirement (%.2f) exceeds 50%% of total investment", hedge.CollateralReq)
	}

	// 检查对冲比例范围
	if hedge.HedgeRatio.LessThan(decimal.NewFromFloat(0.1)) || hedge.HedgeRatio.GreaterThan(c.maxHedgeRatio) {
		return fmt.Errorf("hedge ratio (%.2f) is outside acceptable range (0.1 - %.2f)",
			hedge.HedgeRatio, c.maxHedgeRatio)
	}

	// 检查仓位大小
	if hedge.Size.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("hedge position size must be positive")
	}

	return nil
}

// 估计对冲效果
func (c *Calculator) EstimateHedgeEffectiveness(
	hedge *types.HedgePosition,
	priceMovement decimal.Decimal,
) (map[string]decimal.Decimal, error) {

	if hedge == nil {
		return nil, fmt.Errorf("hedge position is required")
	}

	// 计算对冲仓位盈亏
	var hedgePnL decimal.Decimal
	if hedge.Direction == "long" {
		hedgePnL = priceMovement.Mul(hedge.Size)
	} else {
		hedgePnL = priceMovement.Neg().Mul(hedge.Size)
	}

	// 估计网格仓位暴露 (简化)
	estimatedGridExposure := hedge.Size.Div(hedge.HedgeRatio)
	gridPnL := priceMovement.Mul(estimatedGridExposure)

	// 对冲后的净盈亏
	netPnL := gridPnL.Add(hedgePnL)

	// 对冲效果 (减少多少风险)
	unhedgedRisk := gridPnL.Abs()
	hedgedRisk := netPnL.Abs()

	var effectiveness decimal.Decimal
	if unhedgedRisk.GreaterThan(decimal.Zero) {
		riskReduction := unhedgedRisk.Sub(hedgedRisk)
		effectiveness = riskReduction.Div(unhedgedRisk).Mul(decimal.NewFromInt(100))
	}

	results := map[string]decimal.Decimal{
		"hedge_pnl":      hedgePnL,
		"grid_pnl":       gridPnL,
		"net_pnl":        netPnL,
		"effectiveness":  effectiveness,
		"risk_reduction": unhedgedRisk.Sub(hedgedRisk),
	}

	return results, nil
}
