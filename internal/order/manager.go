package order

/*
订单管理器
*/

import (
	"crypto-arbitrage-trader/internal/exchange"
	"crypto-arbitrage-trader/pkg/types"
	"crypto-arbitrage-trader/pkg/utils/log"
	"fmt"
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

type Manager struct {
	exchange      exchange.Exchange
	orders        map[string]*types.Order
	positions     map[string]*types.Position
	gridOrders    map[string][]*types.Order // symbol -> grid orders
	hedgeOrders   map[string]*types.Order   // symbol -> hedge order
	mu            sync.RWMutex
	stopChan      chan struct{}
	monitorTicker *time.Ticker
}

func NewManager(ex exchange.Exchange) *Manager {
	return &Manager{
		exchange:    ex,
		orders:      make(map[string]*types.Order),
		positions:   make(map[string]*types.Position),
		gridOrders:  make(map[string][]*types.Order),
		hedgeOrders: make(map[string]*types.Order),
		stopChan:    make(chan struct{}),
	}
}

// Start starts the order manager monitoring
func (om *Manager) Start() error {
	om.monitorTicker = time.NewTicker(5 * time.Second)

	go om.monitorOrders()

	log.Info("Order manager started")
	return nil
}

// Stop stops the order manager
func (om *Manager) Stop() error {
	close(om.stopChan)

	if om.monitorTicker != nil {
		om.monitorTicker.Stop()
	}

	log.Info("Order manager stopped")
	return nil
}

// CreateGridOrders creates all grid trading orders
func (om *Manager) CreateGridOrders(
	gridConfig *types.GridParam,
	buyLevels, sellLevels []decimal.Decimal,
) error {
	om.mu.Lock()
	defer om.mu.Unlock()

	symbol := gridConfig.Symbol
	orders := make([]*types.Order, 0)

	// Create buy orders (grid support levels)
	for _, price := range buyLevels {
		orderReq := &exchange.OrderRequest{
			Symbol:      symbol,
			Side:        "buy",
			Type:        "limit",
			Quantity:    gridConfig.OrderSize.Div(price), // Calculate quantity based on USDT amount
			Price:       price,
			TimeInForce: "GTC",
			ClientID:    fmt.Sprintf("grid_buy_%s_%s", symbol, price.String()),
		}

		order, err := om.exchange.PlaceOrder(orderReq)
		if err != nil {
			log.WithError(err).Errorf("Failed to place buy order at %s", price.String())
			continue
		}

		om.orders[order.ID] = order
		orders = append(orders, order)

		log.Infof("Grid buy order placed: %s at %s", order.ID, price.String())
	}

	// Create sell orders (grid resistance levels)
	for _, price := range sellLevels {
		orderReq := &exchange.OrderRequest{
			Symbol:      symbol,
			Side:        "sell",
			Type:        "limit",
			Quantity:    gridConfig.OrderSize.Div(price), // Calculate quantity
			Price:       price,
			TimeInForce: "GTC",
			ClientID:    fmt.Sprintf("grid_sell_%s_%s", symbol, price.String()),
		}

		order, err := om.exchange.PlaceOrder(orderReq)
		if err != nil {
			log.WithError(err).Errorf("Failed to place sell order at %s", price.String())
			continue
		}

		om.orders[order.ID] = order
		orders = append(orders, order)

		log.Infof("Grid sell order placed: %s at %s", order.ID, price.String())
	}

	om.gridOrders[symbol] = orders

	log.Infof("Created %d grid orders for %s", len(orders), symbol)
	return nil
}

// CreateHedgePosition creates hedge position
func (om *Manager) CreateHedgePosition(symbol string, hedgePos *types.HedgePosition) error {
	om.mu.Lock()
	defer om.mu.Unlock()

	orderReq := &exchange.OrderRequest{
		Symbol:   symbol,
		Side:     hedgePos.Direction,
		Type:     "market",
		Quantity: hedgePos.Size,
		ClientID: fmt.Sprintf("hedge_%s_%s", symbol, hedgePos.Direction),
	}

	order, err := om.exchange.PlaceOrder(orderReq)
	if err != nil {
		return fmt.Errorf("failed to create hedge position: %w", err)
	}

	om.orders[order.ID] = order
	om.hedgeOrders[symbol] = order

	log.Infof("Hedge position created: %s %s %s", hedgePos.Direction, hedgePos.Size.String(), symbol)
	return nil
}

// monitorOrders monitors order status and handles fills
func (om *Manager) monitorOrders() {
	for {
		select {
		case <-om.stopChan:
			return
		case <-om.monitorTicker.C:
			om.checkOrderStatus()
		}
	}
}

// checkOrderStatus checks status of all active orders
func (om *Manager) checkOrderStatus() {
	om.mu.RLock()
	orderIDs := make([]string, 0, len(om.orders))
	symbols := make(map[string]bool)

	for id, order := range om.orders {
		if order.Status == "open" || order.Status == "partially_filled" {
			orderIDs = append(orderIDs, id)
			symbols[order.Symbol] = true
		}
	}
	om.mu.RUnlock()

	// Check orders for each symbol
	for symbol := range symbols {
		openOrders, err := om.exchange.GetOpenOrders(symbol)
		if err != nil {
			log.WithError(err).Errorf("Failed to get open orders for %s", symbol)
			continue
		}

		om.updateOrderStatus(openOrders)
	}
}

// updateOrderStatus updates order status and handles filled orders
func (om *Manager) updateOrderStatus(openOrders []types.Order) {
	om.mu.Lock()
	defer om.mu.Unlock()

	openOrderMap := make(map[string]*types.Order)
	for i := range openOrders {
		openOrderMap[openOrders[i].ID] = &openOrders[i]
	}

	for id, order := range om.orders {
		if openOrder, exists := openOrderMap[id]; exists {
			// Order is still open, update status
			om.orders[id] = openOrder
		} else if order.Status == "open" || order.Status == "partially_filled" {
			// Order is no longer in open orders, likely filled
			order.Status = "filled"
			om.orders[id] = order

			om.handleOrderFill(order)
		}
	}
}

// handleOrderFill handles order fill events
func (om *Manager) handleOrderFill(order *types.Order) {
	log.Infof("Order filled: %s %s %s at %s",
		order.Side, order.Quantity.String(), order.Symbol, order.Price.String())

	// Check if this is a grid order
	if om.isGridOrder(order) {
		om.handleGridOrderFill(order)
	}
}

// isGridOrder checks if order is a grid order
func (om *Manager) isGridOrder(order *types.Order) bool {
	orders, exists := om.gridOrders[order.Symbol]
	if !exists {
		return false
	}

	for _, gridOrder := range orders {
		if gridOrder.ID == order.ID {
			return true
		}
	}

	return false
}

// handleGridOrderFill handles grid order fill and creates opposite order
func (om *Manager) handleGridOrderFill(order *types.Order) {
	// Calculate opposite order price (simple implementation)
	var oppositePrice decimal.Decimal
	var oppositeSide string

	if order.Side == "buy" {
		// Create sell order above buy price
		oppositePrice = order.Price.Mul(decimal.NewFromFloat(1.005)) // 0.5% profit
		oppositeSide = "sell"
	} else {
		// Create buy order below sell price
		oppositePrice = order.Price.Mul(decimal.NewFromFloat(0.995)) // 0.5% profit
		oppositeSide = "buy"
	}

	// Place opposite order
	orderReq := &exchange.OrderRequest{
		Symbol:      order.Symbol,
		Side:        oppositeSide,
		Type:        "limit",
		Quantity:    order.Quantity,
		Price:       oppositePrice,
		TimeInForce: "GTC",
		ClientID:    fmt.Sprintf("grid_%s_%s_%s", oppositeSide, order.Symbol, oppositePrice.String()),
	}

	newOrder, err := om.exchange.PlaceOrder(orderReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to place opposite grid order")
		return
	}

	om.orders[newOrder.ID] = newOrder

	// Add to grid orders
	om.gridOrders[order.Symbol] = append(om.gridOrders[order.Symbol], newOrder)

	log.Infof("Opposite grid order placed: %s at %s", newOrder.ID, oppositePrice.String())
}

// CancelAllOrders cancels all orders for a symbol
func (om *Manager) CancelAllOrders(symbol string) error {
	om.mu.Lock()
	defer om.mu.Unlock()

	err := om.exchange.CancelAllOrders(symbol)
	if err != nil {
		return fmt.Errorf("failed to cancel all orders: %w", err)
	}

	// Update local order status
	for id, order := range om.orders {
		if order.Symbol == symbol && (order.Status == "open" || order.Status == "partially_filled") {
			order.Status = "cancelled"
			om.orders[id] = order
		}
	}

	// Clear grid orders
	delete(om.gridOrders, symbol)
	delete(om.hedgeOrders, symbol)

	log.Infof("All orders cancelled for %s", symbol)
	return nil
}

// GetOrderStatus returns current status of an order
func (om *Manager) GetOrderStatus(orderID string) (*types.Order, error) {
	om.mu.RLock()
	defer om.mu.RUnlock()

	order, exists := om.orders[orderID]
	if !exists {
		return nil, fmt.Errorf("order not found: %s", orderID)
	}

	return order, nil
}

// GetActiveOrders returns all active orders for a symbol
func (om *Manager) GetActiveOrders(symbol string) []*types.Order {
	om.mu.RLock()
	defer om.mu.RUnlock()

	var activeOrders []*types.Order

	for _, order := range om.orders {
		if order.Symbol == symbol && (order.Status == "open" || order.Status == "partially_filled") {
			activeOrders = append(activeOrders, order)
		}
	}

	return activeOrders
}

// GetGridOrders returns all grid orders for a symbol
func (om *Manager) GetGridOrders(symbol string) []*types.Order {
	om.mu.RLock()
	defer om.mu.RUnlock()

	orders, exists := om.gridOrders[symbol]
	if !exists {
		return []*types.Order{}
	}

	return orders
}

// GetOrderSummary returns order summary statistics
func (om *Manager) GetOrderSummary(symbol string) map[string]interface{} {
	om.mu.RLock()
	defer om.mu.RUnlock()

	summary := map[string]interface{}{
		"total_orders":     0,
		"active_orders":    0,
		"filled_orders":    0,
		"cancelled_orders": 0,
		"grid_orders":      0,
		"hedge_orders":     0,
	}

	for _, order := range om.orders {
		if order.Symbol == symbol {
			summary["total_orders"] = summary["total_orders"].(int) + 1

			switch order.Status {
			case "open", "partially_filled":
				summary["active_orders"] = summary["active_orders"].(int) + 1
			case "filled":
				summary["filled_orders"] = summary["filled_orders"].(int) + 1
			case "cancelled":
				summary["cancelled_orders"] = summary["cancelled_orders"].(int) + 1
			}
		}
	}

	if orders, exists := om.gridOrders[symbol]; exists {
		summary["grid_orders"] = len(orders)
	}

	if _, exists := om.hedgeOrders[symbol]; exists {
		summary["hedge_orders"] = 1
	}

	return summary
}

// UpdatePositions updates position information
func (om *Manager) UpdatePositions() error {
	positions, err := om.exchange.GetPositions()
	if err != nil {
		return fmt.Errorf("failed to get positions: %w", err)
	}

	om.mu.Lock()
	defer om.mu.Unlock()

	om.positions = make(map[string]*types.Position)
	for i := range positions {
		om.positions[positions[i].Symbol] = &positions[i]
	}

	return nil
}

// GetPosition returns position for a symbol
func (om *Manager) GetPosition(symbol string) (*types.Position, error) {
	om.mu.RLock()
	defer om.mu.RUnlock()

	position, exists := om.positions[symbol]
	if !exists {
		return nil, fmt.Errorf("no position found for %s", symbol)
	}

	return position, nil
}

// CalculateRealizedPnL calculates realized PnL from filled orders
func (om *Manager) CalculateRealizedPnL(symbol string) decimal.Decimal {
	om.mu.RLock()
	defer om.mu.RUnlock()

	var totalPnL decimal.Decimal = decimal.Zero
	var buyVolume, sellVolume decimal.Decimal = decimal.Zero, decimal.Zero
	var buyValue, sellValue decimal.Decimal = decimal.Zero, decimal.Zero

	for _, order := range om.orders {
		if order.Symbol == symbol && order.Status == "filled" {
			orderValue := order.Price.Mul(order.FilledQty)

			if order.Side == "buy" {
				buyVolume = buyVolume.Add(order.FilledQty)
				buyValue = buyValue.Add(orderValue)
			} else {
				sellVolume = sellVolume.Add(order.FilledQty)
				sellValue = sellValue.Add(orderValue)
			}
		}
	}

	// Calculate PnL for completed pairs
	completedVolume := decimal.Min(buyVolume, sellVolume)
	if completedVolume.GreaterThan(decimal.Zero) {
		avgBuyPrice := buyValue.Div(buyVolume)
		avgSellPrice := sellValue.Div(sellVolume)
		totalPnL = avgSellPrice.Sub(avgBuyPrice).Mul(completedVolume)
	}

	return totalPnL
}
