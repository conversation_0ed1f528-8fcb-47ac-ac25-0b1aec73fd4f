package market

/*
市场分析器,主要用于计算价格范围和信心水平
1. 计算基于波动率的范围
2. 计算基本的支持和阻力水平
3. 综合分析并给出最终建议
4. 计算信心水平
5. 提供完整的市场分析和建议
*/

import (
	"crypto-arbitrage-trader/pkg/types"
	"fmt"
	"math"

	"github.com/shopspring/decimal"
)

type Analyzer struct {
	historicalData []PricePoint
}

type PricePoint struct {
	Price     decimal.Decimal
	Volume    decimal.Decimal
	Timestamp int64
}

func NewAnalyzer() *Analyzer {
	return &Analyzer{
		historicalData: make([]PricePoint, 0),
	}
}

// 分析市场并建议价格范围
func (a *Analyzer) AnalyzeMarket(market *types.Market) (*types.PriceRange, error) {
	if market == nil {
		return nil, fmt.Errorf("market data is required")
	}

	currentPrice := market.CurrentPrice
	priceChange := market.PriceChange

	// 计算基于波动率的范围
	volLower, volUpper := a.calculateVolatilityRange(currentPrice, priceChange)

	// 计算支持和阻力水平
	srLower, srUpper := a.calculateSupportResistance(currentPrice)

	// 综合分析并给出最终建议
	priceRange := a.combineAnalysis(currentPrice, volLower, volUpper, srLower, srUpper)

	return priceRange, nil
}

// 计算基于波动率的范围
func (a *Analyzer) calculateVolatilityRange(currentPrice, priceChange decimal.Decimal) (decimal.Decimal, decimal.Decimal) {
	// 使用价格变化作为波动率指标
	volatility := priceChange.Abs().Div(currentPrice).Mul(decimal.NewFromFloat(100))

	// 根据波动率调整范围 (通常为2-5%范围)
	var rangePercent decimal.Decimal
	if volatility.GreaterThan(decimal.NewFromFloat(10)) {
		rangePercent = decimal.NewFromFloat(0.05) // 5% for high volatility
	} else if volatility.GreaterThan(decimal.NewFromFloat(5)) {
		rangePercent = decimal.NewFromFloat(0.03) // 3% for medium volatility
	} else {
		rangePercent = decimal.NewFromFloat(0.02) // 2% for low volatility
	}

	priceDeviation := currentPrice.Mul(rangePercent)
	lowerBound := currentPrice.Sub(priceDeviation)
	upperBound := currentPrice.Add(priceDeviation)

	return lowerBound, upperBound
}

// 计算基本的支持和阻力水平，支持是指价格下跌时，价格会反弹的水平，阻力是指价格上涨时，价格会回落的水平
func (a *Analyzer) calculateSupportResistance(currentPrice decimal.Decimal) (decimal.Decimal, decimal.Decimal) {
	// 简单实现使用心理水平
	// 实际上, 这会使用历史价格数据

	// 找到最近的整数作为心理水平
	priceFloat, _ := currentPrice.Float64()

	// 计算最近的重大水平 (每$100, $1000等)
	var step float64
	if priceFloat > 10000 {
		step = 1000
	} else if priceFloat > 1000 {
		step = 100
	} else if priceFloat > 100 {
		step = 10
	} else {
		step = 1
	}

	lowerLevel := math.Floor(priceFloat/step) * step
	upperLevel := math.Ceil(priceFloat/step) * step

	if upperLevel == priceFloat {
		upperLevel += step
	}
	if lowerLevel == priceFloat {
		lowerLevel -= step
	}

	return decimal.NewFromFloat(lowerLevel), decimal.NewFromFloat(upperLevel)
}

// 综合分析并给出最终建议
func (a *Analyzer) combineAnalysis(currentPrice decimal.Decimal,
	volLower, volUpper, srLower, srUpper decimal.Decimal) *types.PriceRange {

	// 使用更保守的范围
	finalLower := decimal.Max(volLower, srLower)
	finalUpper := decimal.Min(volUpper, srUpper)

	// 确保我们有一个合理的范围
	rangeSize := finalUpper.Sub(finalLower).Div(currentPrice)
	if rangeSize.LessThan(decimal.NewFromFloat(0.01)) {
		// 最小1%范围
		adjustment := currentPrice.Mul(decimal.NewFromFloat(0.005))
		finalLower = currentPrice.Sub(adjustment)
		finalUpper = currentPrice.Add(adjustment)
	}

	// 根据范围大小和市场条件计算信心
	confidence := a.calculateConfidence(rangeSize)

	reasoning := fmt.Sprintf("Based on current price %.2f, volatility analysis suggests %.2f-%.2f range",
		currentPrice, finalLower, finalUpper)

	return &types.PriceRange{
		LowerBound: finalLower,
		UpperBound: finalUpper,
		Confidence: confidence,
		Reasoning:  reasoning,
	}
}

// 计算信心水平, 信心水平越高，表示价格范围越可靠, 范围越小，信心水平越高
func (a *Analyzer) calculateConfidence(rangeSize decimal.Decimal) float64 {
	rangeSizeFloat, _ := rangeSize.Float64()

	// 更高的信心水平用于中等范围 (2-4%)
	if rangeSizeFloat >= 0.02 && rangeSizeFloat <= 0.04 {
		return 0.85
	} else if rangeSizeFloat >= 0.01 && rangeSizeFloat <= 0.06 {
		return 0.75
	} else {
		return 0.65
	}
}

// 提供完整的市场分析和建议
func (a *Analyzer) GetMarketSuggestion(symbol string, currentPrice decimal.Decimal,
	volume24h, priceChange decimal.Decimal) (*types.Market, *types.PriceRange, error) {

	market := &types.Market{
		Symbol:       symbol,
		CurrentPrice: currentPrice,
		Volume24h:    volume24h,
		PriceChange:  priceChange,
	}

	priceRange, err := a.AnalyzeMarket(market)
	if err != nil {
		return nil, nil, err
	}

	return market, priceRange, nil
}
