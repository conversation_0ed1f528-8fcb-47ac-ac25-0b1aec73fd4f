package exchange

import (
	"crypto-arbitrage-trader/pkg/types"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

type GateExchange struct {
	apiKey     string
	secretKey  string
	baseURL    string
	client     *http.Client
	connected  bool
}

func NewGateExchange(config map[string]string) (*GateExchange, error) {
	apiKey, ok := config["api_key"]
	if !ok {
		return nil, fmt.Errorf("api_key is required for Gate.io")
	}
	
	secretKey, ok := config["secret_key"]
	if !ok {
		return nil, fmt.Errorf("secret_key is required for Gate.io")
	}
	
	baseURL := config["base_url"]
	if baseURL == "" {
		baseURL = "https://api.gateio.ws"
	}
	
	return &GateExchange{
		apiKey:    api<PERSON><PERSON>,
		secretKey: secretKey,
		baseURL:   baseURL,
		client:    &http.Client{Timeout: 30 * time.Second},
		connected: false,
	}, nil
}

func (g *GateExchange) GetName() string {
	return "gate"
}

func (g *GateExchange) Connect() error {
	// Test connection
	_, err := g.makeRequest("GET", "/api/v4/spot/time", nil, false)
	if err != nil {
		return fmt.Errorf("failed to connect to Gate.io: %w", err)
	}
	
	g.connected = true
	return nil
}

func (g *GateExchange) Disconnect() error {
	g.connected = false
	return nil
}

func (g *GateExchange) IsConnected() bool {
	return g.connected
}

func (g *GateExchange) GetTicker(symbol string) (*types.Market, error) {
	endpoint := fmt.Sprintf("/api/v4/spot/tickers?currency_pair=%s", strings.ToUpper(symbol))
	
	data, err := g.makeRequest("GET", endpoint, nil, false)
	if err != nil {
		return nil, err
	}
	
	var tickers []struct {
		CurrencyPair     string `json:"currency_pair"`
		Last             string `json:"last"`
		ChangePercentage string `json:"change_percentage"`
		BaseVolume       string `json:"base_volume"`
	}
	
	if err := json.Unmarshal(data, &tickers); err != nil {
		return nil, err
	}
	
	if len(tickers) == 0 {
		return nil, fmt.Errorf("no ticker data found for symbol: %s", symbol)
	}
	
	ticker := tickers[0]
	price, _ := decimal.NewFromString(ticker.Last)
	changePercent, _ := decimal.NewFromString(ticker.ChangePercentage)
	volume, _ := decimal.NewFromString(ticker.BaseVolume)
	
	// Calculate price change from percentage
	priceChange := price.Mul(changePercent).Div(decimal.NewFromInt(100))
	
	return &types.Market{
		Symbol:       ticker.CurrencyPair,
		CurrentPrice: price,
		Volume24h:    volume,
		PriceChange:  priceChange,
	}, nil
}

func (g *GateExchange) GetOrderBook(symbol string, depth int) (*OrderBook, error) {
	endpoint := fmt.Sprintf("/api/v4/spot/order_book?currency_pair=%s&limit=%d", 
		strings.ToUpper(symbol), depth)
	
	data, err := g.makeRequest("GET", endpoint, nil, false)
	if err != nil {
		return nil, err
	}
	
	var response struct {
		Bids [][]string `json:"bids"`
		Asks [][]string `json:"asks"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	orderBook := &OrderBook{
		Symbol:    symbol,
		Timestamp: time.Now().Unix(),
		Bids:      make([]PriceLevel, len(response.Bids)),
		Asks:      make([]PriceLevel, len(response.Asks)),
	}
	
	for i, bid := range response.Bids {
		price, _ := decimal.NewFromString(bid[0])
		quantity, _ := decimal.NewFromString(bid[1])
		orderBook.Bids[i] = PriceLevel{Price: price, Quantity: quantity}
	}
	
	for i, ask := range response.Asks {
		price, _ := decimal.NewFromString(ask[0])
		quantity, _ := decimal.NewFromString(ask[1])
		orderBook.Asks[i] = PriceLevel{Price: price, Quantity: quantity}
	}
	
	return orderBook, nil
}

func (g *GateExchange) GetKlines(symbol string, interval string, limit int) ([]Kline, error) {
	endpoint := fmt.Sprintf("/api/v4/spot/candlesticks?currency_pair=%s&interval=%s&limit=%d",
		strings.ToUpper(symbol), interval, limit)
	
	data, err := g.makeRequest("GET", endpoint, nil, false)
	if err != nil {
		return nil, err
	}
	
	var response [][]string
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	klines := make([]Kline, len(response))
	for i, k := range response {
		openTime, _ := strconv.ParseInt(k[0], 10, 64)
		volume, _ := decimal.NewFromString(k[1])
		close, _ := decimal.NewFromString(k[2])
		high, _ := decimal.NewFromString(k[3])
		low, _ := decimal.NewFromString(k[4])
		open, _ := decimal.NewFromString(k[5])
		
		klines[i] = Kline{
			OpenTime:  openTime,
			CloseTime: openTime + 60, // Simplified
			Open:      open,
			High:      high,
			Low:       low,
			Close:     close,
			Volume:    volume,
		}
	}
	
	return klines, nil
}

func (g *GateExchange) GetBalance() (*AccountBalance, error) {
	data, err := g.makeRequest("GET", "/api/v4/spot/accounts", nil, true)
	if err != nil {
		return nil, err
	}
	
	var response []struct {
		Currency  string `json:"currency"`
		Available string `json:"available"`
		Locked    string `json:"locked"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	assets := make(map[string]AssetBalance)
	totalBalance := decimal.Zero
	availableBalance := decimal.Zero
	lockedBalance := decimal.Zero
	
	for _, balance := range response {
		available, _ := decimal.NewFromString(balance.Available)
		locked, _ := decimal.NewFromString(balance.Locked)
		total := available.Add(locked)
		
		if total.GreaterThan(decimal.Zero) {
			assets[balance.Currency] = AssetBalance{
				Asset:  balance.Currency,
				Free:   available,
				Locked: locked,
				Total:  total,
			}
			
			// Calculate in USDT equivalent (simplified)
			if balance.Currency == "USDT" {
				totalBalance = totalBalance.Add(total)
				availableBalance = availableBalance.Add(available)
				lockedBalance = lockedBalance.Add(locked)
			}
		}
	}
	
	return &AccountBalance{
		TotalBalance:     totalBalance,
		AvailableBalance: availableBalance,
		LockedBalance:    lockedBalance,
		Assets:           assets,
	}, nil
}

func (g *GateExchange) PlaceOrder(req *OrderRequest) (*types.Order, error) {
	params := map[string]interface{}{
		"currency_pair": strings.ToUpper(req.Symbol),
		"side":          req.Side,
		"type":          req.Type,
		"amount":        req.Quantity.String(),
	}
	
	if req.Price.GreaterThan(decimal.Zero) {
		params["price"] = req.Price.String()
	}
	
	if req.TimeInForce != "" {
		params["time_in_force"] = req.TimeInForce
	}
	
	jsonData, _ := json.Marshal(params)
	
	data, err := g.makeRequestWithBody("POST", "/api/v4/spot/orders", jsonData, true)
	if err != nil {
		return nil, err
	}
	
	var response struct {
		Id           string `json:"id"`
		CurrencyPair string `json:"currency_pair"`
		Status       string `json:"status"`
		Amount       string `json:"amount"`
		Price        string `json:"price"`
		Side         string `json:"side"`
		Type         string `json:"type"`
		CreateTime   string `json:"create_time"`
		FilledTotal  string `json:"filled_total"`
		Left         string `json:"left"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	price, _ := decimal.NewFromString(response.Price)
	quantity, _ := decimal.NewFromString(response.Amount)
	left, _ := decimal.NewFromString(response.Left)
	filledQty := quantity.Sub(left)
	
	createTime, _ := strconv.ParseInt(response.CreateTime, 10, 64)
	
	return &types.Order{
		ID:        response.Id,
		Symbol:    response.CurrencyPair,
		Side:      response.Side,
		Type:      response.Type,
		Quantity:  quantity,
		Price:     price,
		Status:    response.Status,
		CreatedAt: time.Unix(createTime, 0),
		FilledQty: filledQty,
		RemainQty: left,
	}, nil
}

func (g *GateExchange) CancelOrder(symbol, orderID string) error {
	endpoint := fmt.Sprintf("/api/v4/spot/orders/%s?currency_pair=%s", orderID, strings.ToUpper(symbol))
	_, err := g.makeRequest("DELETE", endpoint, nil, true)
	return err
}

func (g *GateExchange) CancelAllOrders(symbol string) error {
	endpoint := fmt.Sprintf("/api/v4/spot/orders?currency_pair=%s", strings.ToUpper(symbol))
	_, err := g.makeRequest("DELETE", endpoint, nil, true)
	return err
}

func (g *GateExchange) GetOpenOrders(symbol string) ([]types.Order, error) {
	endpoint := "/api/v4/spot/open_orders"
	if symbol != "" {
		endpoint += "?currency_pair=" + strings.ToUpper(symbol)
	}
	
	data, err := g.makeRequest("GET", endpoint, nil, true)
	if err != nil {
		return nil, err
	}
	
	var response []struct {
		Id           string `json:"id"`
		CurrencyPair string `json:"currency_pair"`
		Status       string `json:"status"`
		Amount       string `json:"amount"`
		Price        string `json:"price"`
		Side         string `json:"side"`
		Type         string `json:"type"`
		CreateTime   string `json:"create_time"`
		Left         string `json:"left"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	orders := make([]types.Order, len(response))
	for i, order := range response {
		price, _ := decimal.NewFromString(order.Price)
		quantity, _ := decimal.NewFromString(order.Amount)
		left, _ := decimal.NewFromString(order.Left)
		filledQty := quantity.Sub(left)
		
		createTime, _ := strconv.ParseInt(order.CreateTime, 10, 64)
		
		orders[i] = types.Order{
			ID:        order.Id,
			Symbol:    order.CurrencyPair,
			Side:      order.Side,
			Type:      order.Type,
			Quantity:  quantity,
			Price:     price,
			Status:    order.Status,
			CreatedAt: time.Unix(createTime, 0),
			FilledQty: filledQty,
			RemainQty: left,
		}
	}
	
	return orders, nil
}

// Futures trading methods (placeholder implementations)
func (g *GateExchange) GetPositions() ([]types.Position, error) {
	return []types.Position{}, fmt.Errorf("futures positions not implemented")
}

func (g *GateExchange) OpenPosition(req *PositionRequest) (*types.Position, error) {
	return nil, fmt.Errorf("futures positions not implemented")
}

func (g *GateExchange) ClosePosition(symbol string, size decimal.Decimal) error {
	return fmt.Errorf("futures positions not implemented")
}

func (g *GateExchange) GetPosition(symbol string) (*types.Position, error) {
	return nil, fmt.Errorf("futures positions not implemented")
}

func (g *GateExchange) GetExchangeInfo() (*ExchangeInfo, error) {
	data, err := g.makeRequest("GET", "/api/v4/spot/currency_pairs", nil, false)
	if err != nil {
		return nil, err
	}
	
	var response []struct {
		Id               string `json:"id"`
		Base             string `json:"base"`
		Quote            string `json:"quote"`
		TradeStatus      string `json:"trade_status"`
		MinBaseAmount    string `json:"min_base_amount"`
		MinQuoteAmount   string `json:"min_quote_amount"`
		AmountPrecision  int    `json:"amount_precision"`
		Precision        int    `json:"precision"`
		Fee              string `json:"fee"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	symbols := make([]SymbolInfo, len(response))
	for i, s := range response {
		minOrderSize, _ := decimal.NewFromString(s.MinBaseAmount)
		fee, _ := decimal.NewFromString(s.Fee)
		priceStep := decimal.NewFromFloat(1.0).Div(decimal.NewFromInt(int64(10)).Pow(decimal.NewFromInt(int64(s.Precision))))
		quantityStep := decimal.NewFromFloat(1.0).Div(decimal.NewFromInt(int64(10)).Pow(decimal.NewFromInt(int64(s.AmountPrecision))))
		
		symbols[i] = SymbolInfo{
			Symbol:              s.Id,
			BaseAsset:           s.Base,
			QuoteAsset:          s.Quote,
			Status:              s.TradeStatus,
			MinOrderSize:        minOrderSize,
			PriceStep:           priceStep,
			QuantityStep:        quantityStep,
			MakerFee:            fee,
			TakerFee:            fee,
			SupportedOrderTypes: []string{"limit", "market"},
		}
	}
	
	return &ExchangeInfo{
		Name:     "Gate.io",
		Symbols:  symbols,
		Timezone: "UTC",
	}, nil
}

// makeRequest makes HTTP request to Gate.io API
func (g *GateExchange) makeRequest(method, endpoint string, params url.Values, signed bool) ([]byte, error) {
	return g.makeRequestWithBody(method, endpoint, nil, signed)
}

func (g *GateExchange) makeRequestWithBody(method, endpoint string, body []byte, signed bool) ([]byte, error) {
	fullURL := g.baseURL + endpoint
	
	var req *http.Request
	var err error
	
	if body != nil {
		req, err = http.NewRequest(method, fullURL, strings.NewReader(string(body)))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, err = http.NewRequest(method, fullURL, nil)
	}
	
	if err != nil {
		return nil, err
	}
	
	if signed {
		timestamp := strconv.FormatInt(time.Now().Unix(), 10)
		
		var payloadToSign string
		if body != nil {
			payloadToSign = string(body)
		}
		
		signature := g.sign(method, endpoint, "", payloadToSign, timestamp)
		
		req.Header.Set("KEY", g.apiKey)
		req.Header.Set("Timestamp", timestamp)
		req.Header.Set("SIGN", signature)
	}
	
	resp, err := g.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("API error: %s", string(responseBody))
	}
	
	return responseBody, nil
}

// sign creates HMAC SHA512 signature for Gate.io
func (g *GateExchange) sign(method, uri, query, body, timestamp string) string {
	message := fmt.Sprintf("%s\n%s\n%s\n%s\n%s", method, uri, query, body, timestamp)
	h := hmac.New(sha512.New, []byte(g.secretKey))
	h.Write([]byte(message))
	return hex.EncodeToString(h.Sum(nil))
}