package exchange

import (
	"crypto-arbitrage-trader/pkg/types"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

type BinanceExchange struct {
	apiKey     string
	secretKey  string
	baseURL    string
	client     *http.Client
	connected  bool
}

func NewBinanceExchange(config map[string]string) (*BinanceExchange, error) {
	apiKey, ok := config["api_key"]
	if !ok {
		return nil, fmt.Errorf("api_key is required for Binance")
	}
	
	secretKey, ok := config["secret_key"]
	if !ok {
		return nil, fmt.Errorf("secret_key is required for Binance")
	}
	
	baseURL := config["base_url"]
	if baseURL == "" {
		baseURL = "https://api.binance.com"
	}
	
	return &BinanceExchange{
		apiKey:    api<PERSON><PERSON>,
		secretKey: secretKey,
		baseURL:   baseURL,
		client:    &http.Client{Timeout: 30 * time.Second},
		connected: false,
	}, nil
}

func (b *BinanceExchange) GetName() string {
	return "binance"
}

func (b *BinanceExchange) Connect() error {
	// Test connection with server time
	_, err := b.makeRequest("GET", "/api/v3/time", nil, false)
	if err != nil {
		return fmt.Errorf("failed to connect to Binance: %w", err)
	}
	
	b.connected = true
	return nil
}

func (b *BinanceExchange) Disconnect() error {
	b.connected = false
	return nil
}

func (b *BinanceExchange) IsConnected() bool {
	return b.connected
}

func (b *BinanceExchange) GetTicker(symbol string) (*types.Market, error) {
	params := url.Values{}
	params.Set("symbol", strings.ToUpper(symbol))
	
	data, err := b.makeRequest("GET", "/api/v3/ticker/24hr", params, false)
	if err != nil {
		return nil, err
	}
	
	var ticker struct {
		Symbol             string `json:"symbol"`
		PriceChange        string `json:"priceChange"`
		PriceChangePercent string `json:"priceChangePercent"`
		LastPrice          string `json:"lastPrice"`
		Volume             string `json:"volume"`
	}
	
	if err := json.Unmarshal(data, &ticker); err != nil {
		return nil, err
	}
	
	price, _ := decimal.NewFromString(ticker.LastPrice)
	priceChange, _ := decimal.NewFromString(ticker.PriceChange)
	volume, _ := decimal.NewFromString(ticker.Volume)
	
	return &types.Market{
		Symbol:       ticker.Symbol,
		CurrentPrice: price,
		Volume24h:    volume,
		PriceChange:  priceChange,
	}, nil
}

func (b *BinanceExchange) GetOrderBook(symbol string, depth int) (*OrderBook, error) {
	params := url.Values{}
	params.Set("symbol", strings.ToUpper(symbol))
	params.Set("limit", strconv.Itoa(depth))
	
	data, err := b.makeRequest("GET", "/api/v3/depth", params, false)
	if err != nil {
		return nil, err
	}
	
	var response struct {
		LastUpdateId int64      `json:"lastUpdateId"`
		Bids         [][]string `json:"bids"`
		Asks         [][]string `json:"asks"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	orderBook := &OrderBook{
		Symbol:    symbol,
		Timestamp: time.Now().Unix(),
		Bids:      make([]PriceLevel, len(response.Bids)),
		Asks:      make([]PriceLevel, len(response.Asks)),
	}
	
	for i, bid := range response.Bids {
		price, _ := decimal.NewFromString(bid[0])
		quantity, _ := decimal.NewFromString(bid[1])
		orderBook.Bids[i] = PriceLevel{Price: price, Quantity: quantity}
	}
	
	for i, ask := range response.Asks {
		price, _ := decimal.NewFromString(ask[0])
		quantity, _ := decimal.NewFromString(ask[1])
		orderBook.Asks[i] = PriceLevel{Price: price, Quantity: quantity}
	}
	
	return orderBook, nil
}

func (b *BinanceExchange) GetKlines(symbol string, interval string, limit int) ([]Kline, error) {
	params := url.Values{}
	params.Set("symbol", strings.ToUpper(symbol))
	params.Set("interval", interval)
	params.Set("limit", strconv.Itoa(limit))
	
	data, err := b.makeRequest("GET", "/api/v3/klines", params, false)
	if err != nil {
		return nil, err
	}
	
	var response [][]interface{}
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	klines := make([]Kline, len(response))
	for i, k := range response {
		openTime := int64(k[0].(float64))
		closeTime := int64(k[6].(float64))
		open, _ := decimal.NewFromString(k[1].(string))
		high, _ := decimal.NewFromString(k[2].(string))
		low, _ := decimal.NewFromString(k[3].(string))
		close, _ := decimal.NewFromString(k[4].(string))
		volume, _ := decimal.NewFromString(k[5].(string))
		
		klines[i] = Kline{
			OpenTime:  openTime,
			CloseTime: closeTime,
			Open:      open,
			High:      high,
			Low:       low,
			Close:     close,
			Volume:    volume,
		}
	}
	
	return klines, nil
}

func (b *BinanceExchange) GetBalance() (*AccountBalance, error) {
	data, err := b.makeRequest("GET", "/api/v3/account", nil, true)
	if err != nil {
		return nil, err
	}
	
	var response struct {
		Balances []struct {
			Asset  string `json:"asset"`
			Free   string `json:"free"`
			Locked string `json:"locked"`
		} `json:"balances"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	assets := make(map[string]AssetBalance)
	totalBalance := decimal.Zero
	availableBalance := decimal.Zero
	lockedBalance := decimal.Zero
	
	for _, balance := range response.Balances {
		free, _ := decimal.NewFromString(balance.Free)
		locked, _ := decimal.NewFromString(balance.Locked)
		total := free.Add(locked)
		
		if total.GreaterThan(decimal.Zero) {
			assets[balance.Asset] = AssetBalance{
				Asset:  balance.Asset,
				Free:   free,
				Locked: locked,
				Total:  total,
			}
			
			// For simplification, calculate in USDT equivalent
			// In practice, you'd need to convert each asset to base currency
			if balance.Asset == "USDT" || balance.Asset == "BUSD" {
				totalBalance = totalBalance.Add(total)
				availableBalance = availableBalance.Add(free)
				lockedBalance = lockedBalance.Add(locked)
			}
		}
	}
	
	return &AccountBalance{
		TotalBalance:     totalBalance,
		AvailableBalance: availableBalance,
		LockedBalance:    lockedBalance,
		Assets:           assets,
	}, nil
}

func (b *BinanceExchange) PlaceOrder(req *OrderRequest) (*types.Order, error) {
	params := url.Values{}
	params.Set("symbol", strings.ToUpper(req.Symbol))
	params.Set("side", strings.ToUpper(req.Side))
	params.Set("type", strings.ToUpper(req.Type))
	params.Set("quantity", req.Quantity.String())
	
	if req.Price.GreaterThan(decimal.Zero) {
		params.Set("price", req.Price.String())
	}
	
	if req.TimeInForce != "" {
		params.Set("timeInForce", req.TimeInForce)
	} else if req.Type == "LIMIT" {
		params.Set("timeInForce", "GTC")
	}
	
	if req.ClientID != "" {
		params.Set("newClientOrderId", req.ClientID)
	}
	
	data, err := b.makeRequest("POST", "/api/v3/order", params, true)
	if err != nil {
		return nil, err
	}
	
	var response struct {
		Symbol        string `json:"symbol"`
		OrderId       int64  `json:"orderId"`
		ClientOrderId string `json:"clientOrderId"`
		TransactTime  int64  `json:"transactTime"`
		Price         string `json:"price"`
		OrigQty       string `json:"origQty"`
		ExecutedQty   string `json:"executedQty"`
		Status        string `json:"status"`
		Side          string `json:"side"`
		Type          string `json:"type"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	price, _ := decimal.NewFromString(response.Price)
	quantity, _ := decimal.NewFromString(response.OrigQty)
	filledQty, _ := decimal.NewFromString(response.ExecutedQty)
	remainQty := quantity.Sub(filledQty)
	
	return &types.Order{
		ID:        strconv.FormatInt(response.OrderId, 10),
		Symbol:    response.Symbol,
		Side:      strings.ToLower(response.Side),
		Type:      strings.ToLower(response.Type),
		Quantity:  quantity,
		Price:     price,
		Status:    strings.ToLower(response.Status),
		CreatedAt: time.Unix(response.TransactTime/1000, 0),
		FilledQty: filledQty,
		RemainQty: remainQty,
	}, nil
}

func (b *BinanceExchange) CancelOrder(symbol, orderID string) error {
	params := url.Values{}
	params.Set("symbol", strings.ToUpper(symbol))
	params.Set("orderId", orderID)
	
	_, err := b.makeRequest("DELETE", "/api/v3/order", params, true)
	return err
}

func (b *BinanceExchange) CancelAllOrders(symbol string) error {
	params := url.Values{}
	params.Set("symbol", strings.ToUpper(symbol))
	
	_, err := b.makeRequest("DELETE", "/api/v3/openOrders", params, true)
	return err
}

func (b *BinanceExchange) GetOpenOrders(symbol string) ([]types.Order, error) {
	params := url.Values{}
	if symbol != "" {
		params.Set("symbol", strings.ToUpper(symbol))
	}
	
	data, err := b.makeRequest("GET", "/api/v3/openOrders", params, true)
	if err != nil {
		return nil, err
	}
	
	var response []struct {
		Symbol        string `json:"symbol"`
		OrderId       int64  `json:"orderId"`
		ClientOrderId string `json:"clientOrderId"`
		Price         string `json:"price"`
		OrigQty       string `json:"origQty"`
		ExecutedQty   string `json:"executedQty"`
		Status        string `json:"status"`
		Side          string `json:"side"`
		Type          string `json:"type"`
		Time          int64  `json:"time"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	orders := make([]types.Order, len(response))
	for i, order := range response {
		price, _ := decimal.NewFromString(order.Price)
		quantity, _ := decimal.NewFromString(order.OrigQty)
		filledQty, _ := decimal.NewFromString(order.ExecutedQty)
		remainQty := quantity.Sub(filledQty)
		
		orders[i] = types.Order{
			ID:        strconv.FormatInt(order.OrderId, 10),
			Symbol:    order.Symbol,
			Side:      strings.ToLower(order.Side),
			Type:      strings.ToLower(order.Type),
			Quantity:  quantity,
			Price:     price,
			Status:    strings.ToLower(order.Status),
			CreatedAt: time.Unix(order.Time/1000, 0),
			FilledQty: filledQty,
			RemainQty: remainQty,
		}
	}
	
	return orders, nil
}

// For futures trading (simplified implementation)
func (b *BinanceExchange) GetPositions() ([]types.Position, error) {
	// This would be implemented for Binance Futures API
	return []types.Position{}, fmt.Errorf("futures positions not implemented for spot trading")
}

func (b *BinanceExchange) OpenPosition(req *PositionRequest) (*types.Position, error) {
	// This would be implemented for Binance Futures API
	return nil, fmt.Errorf("futures positions not implemented for spot trading")
}

func (b *BinanceExchange) ClosePosition(symbol string, size decimal.Decimal) error {
	// This would be implemented for Binance Futures API
	return fmt.Errorf("futures positions not implemented for spot trading")
}

func (b *BinanceExchange) GetPosition(symbol string) (*types.Position, error) {
	// This would be implemented for Binance Futures API
	return nil, fmt.Errorf("futures positions not implemented for spot trading")
}

func (b *BinanceExchange) GetExchangeInfo() (*ExchangeInfo, error) {
	data, err := b.makeRequest("GET", "/api/v3/exchangeInfo", nil, false)
	if err != nil {
		return nil, err
	}
	
	var response struct {
		Timezone string `json:"timezone"`
		Symbols  []struct {
			Symbol     string `json:"symbol"`
			BaseAsset  string `json:"baseAsset"`
			QuoteAsset string `json:"quoteAsset"`
			Status     string `json:"status"`
			Filters    []struct {
				FilterType  string `json:"filterType"`
				MinPrice    string `json:"minPrice,omitempty"`
				MaxPrice    string `json:"maxPrice,omitempty"`
				TickSize    string `json:"tickSize,omitempty"`
				MinQty      string `json:"minQty,omitempty"`
				MaxQty      string `json:"maxQty,omitempty"`
				StepSize    string `json:"stepSize,omitempty"`
			} `json:"filters"`
		} `json:"symbols"`
	}
	
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	
	symbols := make([]SymbolInfo, len(response.Symbols))
	for i, s := range response.Symbols {
		symbolInfo := SymbolInfo{
			Symbol:              s.Symbol,
			BaseAsset:           s.BaseAsset,
			QuoteAsset:          s.QuoteAsset,
			Status:              s.Status,
			SupportedOrderTypes: []string{"LIMIT", "MARKET", "STOP_LOSS", "STOP_LOSS_LIMIT"},
		}
		
		// Parse filters
		for _, filter := range s.Filters {
			switch filter.FilterType {
			case "PRICE_FILTER":
				if filter.MinPrice != "" {
					symbolInfo.MinPrice, _ = decimal.NewFromString(filter.MinPrice)
				}
				if filter.MaxPrice != "" {
					symbolInfo.MaxPrice, _ = decimal.NewFromString(filter.MaxPrice)
				}
				if filter.TickSize != "" {
					symbolInfo.PriceStep, _ = decimal.NewFromString(filter.TickSize)
				}
			case "LOT_SIZE":
				if filter.MinQty != "" {
					symbolInfo.MinOrderSize, _ = decimal.NewFromString(filter.MinQty)
				}
				if filter.MaxQty != "" {
					symbolInfo.MaxOrderSize, _ = decimal.NewFromString(filter.MaxQty)
				}
				if filter.StepSize != "" {
					symbolInfo.QuantityStep, _ = decimal.NewFromString(filter.StepSize)
				}
			}
		}
		
		symbols[i] = symbolInfo
	}
	
	return &ExchangeInfo{
		Name:     "Binance",
		Symbols:  symbols,
		Timezone: response.Timezone,
	}, nil
}

// makeRequest makes HTTP request to Binance API
func (b *BinanceExchange) makeRequest(method, endpoint string, params url.Values, signed bool) ([]byte, error) {
	if params == nil {
		params = url.Values{}
	}
	
	if signed {
		timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
		params.Set("timestamp", timestamp)
		
		queryString := params.Encode()
		signature := b.sign(queryString)
		params.Set("signature", signature)
	}
	
	var req *http.Request
	var err error
	
	if method == "GET" || method == "DELETE" {
		fullURL := b.baseURL + endpoint
		if len(params) > 0 {
			fullURL += "?" + params.Encode()
		}
		req, err = http.NewRequest(method, fullURL, nil)
	} else {
		req, err = http.NewRequest(method, b.baseURL+endpoint, strings.NewReader(params.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	}
	
	if err != nil {
		return nil, err
	}
	
	req.Header.Set("X-MBX-APIKEY", b.apiKey)
	
	resp, err := b.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API error: %s", string(body))
	}
	
	return body, nil
}

// sign creates HMAC SHA256 signature
func (b *BinanceExchange) sign(queryString string) string {
	h := hmac.New(sha256.New, []byte(b.secretKey))
	h.Write([]byte(queryString))
	return hex.EncodeToString(h.Sum(nil))
}