package exchange

import (
	"crypto-arbitrage-trader/pkg/types"
	"fmt"

	"github.com/shopspring/decimal"
)

// Exchange defines the interface that all exchange implementations must satisfy
type Exchange interface {
	// Authentication and connection
	Connect() error
	Disconnect() error
	IsConnected() bool
	
	// Market data
	GetTicker(symbol string) (*types.Market, error)
	GetOrderBook(symbol string, depth int) (*OrderBook, error)
	GetKlines(symbol string, interval string, limit int) ([]Kline, error)
	
	// Account operations
	GetBalance() (*AccountBalance, error)
	GetPositions() ([]types.Position, error)
	GetOpenOrders(symbol string) ([]types.Order, error)
	
	// Trading operations
	PlaceOrder(req *OrderRequest) (*types.Order, error)
	CancelOrder(symbol, orderID string) error
	CancelAllOrders(symbol string) error
	
	// Position management (for futures)
	OpenPosition(req *PositionRequest) (*types.Position, error)
	ClosePosition(symbol string, size decimal.Decimal) error
	GetPosition(symbol string) (*types.Position, error)
	
	// Exchange specific info
	GetExchangeInfo() (*ExchangeInfo, error)
	GetName() string
}

// OrderBook represents order book data
type OrderBook struct {
	Symbol    string              `json:"symbol"`
	Timestamp int64               `json:"timestamp"`
	Bids      []PriceLevel        `json:"bids"`
	Asks      []PriceLevel        `json:"asks"`
}

type PriceLevel struct {
	Price    decimal.Decimal `json:"price"`
	Quantity decimal.Decimal `json:"quantity"`
}

// Kline represents candlestick data
type Kline struct {
	OpenTime  int64           `json:"open_time"`
	CloseTime int64           `json:"close_time"`
	Open      decimal.Decimal `json:"open"`
	High      decimal.Decimal `json:"high"`
	Low       decimal.Decimal `json:"low"`
	Close     decimal.Decimal `json:"close"`
	Volume    decimal.Decimal `json:"volume"`
}

// AccountBalance represents account balance information
type AccountBalance struct {
	TotalBalance     decimal.Decimal            `json:"total_balance"`
	AvailableBalance decimal.Decimal            `json:"available_balance"`
	LockedBalance    decimal.Decimal            `json:"locked_balance"`
	Assets           map[string]AssetBalance    `json:"assets"`
}

type AssetBalance struct {
	Asset     string          `json:"asset"`
	Free      decimal.Decimal `json:"free"`
	Locked    decimal.Decimal `json:"locked"`
	Total     decimal.Decimal `json:"total"`
}

// OrderRequest represents a request to place an order
type OrderRequest struct {
	Symbol      string          `json:"symbol"`
	Side        string          `json:"side"`        // "buy" or "sell"
	Type        string          `json:"type"`        // "limit", "market", "stop"
	Quantity    decimal.Decimal `json:"quantity"`
	Price       decimal.Decimal `json:"price,omitempty"`
	StopPrice   decimal.Decimal `json:"stop_price,omitempty"`
	TimeInForce string          `json:"time_in_force,omitempty"` // "GTC", "IOC", "FOK"
	ClientID    string          `json:"client_id,omitempty"`
}

// PositionRequest represents a request to open a position (futures)
type PositionRequest struct {
	Symbol    string          `json:"symbol"`
	Side      string          `json:"side"`      // "long" or "short"
	Size      decimal.Decimal `json:"size"`
	Leverage  int             `json:"leverage"`
	OrderType string          `json:"order_type"` // "market" or "limit"
	Price     decimal.Decimal `json:"price,omitempty"`
}

// ExchangeInfo represents exchange trading rules and information
type ExchangeInfo struct {
	Name     string       `json:"name"`
	Symbols  []SymbolInfo `json:"symbols"`
	Timezone string       `json:"timezone"`
}

type SymbolInfo struct {
	Symbol              string          `json:"symbol"`
	BaseAsset           string          `json:"base_asset"`
	QuoteAsset          string          `json:"quote_asset"`
	Status              string          `json:"status"`
	MinOrderSize        decimal.Decimal `json:"min_order_size"`
	MaxOrderSize        decimal.Decimal `json:"max_order_size"`
	MinPrice            decimal.Decimal `json:"min_price"`
	MaxPrice            decimal.Decimal `json:"max_price"`
	PriceStep           decimal.Decimal `json:"price_step"`
	QuantityStep        decimal.Decimal `json:"quantity_step"`
	MakerFee            decimal.Decimal `json:"maker_fee"`
	TakerFee            decimal.Decimal `json:"taker_fee"`
	SupportedOrderTypes []string        `json:"supported_order_types"`
}

// WebSocketHandler defines interface for handling real-time data
type WebSocketHandler interface {
	OnTicker(symbol string, ticker *types.Market)
	OnOrderBook(symbol string, orderBook *OrderBook)
	OnTrade(symbol string, trade *Trade)
	OnOrder(order *types.Order)
	OnPosition(position *types.Position)
	OnError(err error)
}

type Trade struct {
	Symbol    string          `json:"symbol"`
	ID        string          `json:"id"`
	Price     decimal.Decimal `json:"price"`
	Quantity  decimal.Decimal `json:"quantity"`
	Side      string          `json:"side"`
	Timestamp int64           `json:"timestamp"`
}

// ExchangeFactory creates exchange instances
type ExchangeFactory struct{}

func NewExchangeFactory() *ExchangeFactory {
	return &ExchangeFactory{}
}

func (f *ExchangeFactory) CreateExchange(exchangeName string, config map[string]string) (Exchange, error) {
	switch exchangeName {
	case "binance":
		return NewBinanceExchange(config)
	case "gate":
		return NewGateExchange(config)
	default:
		return nil, fmt.Errorf("unsupported exchange: %s", exchangeName)
	}
}