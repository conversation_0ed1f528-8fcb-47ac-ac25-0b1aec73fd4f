package risk

/*
风险管理器,主要用于计算风险参数,如止损,止盈,最大回撤,每日目标
*/

import (
	"crypto-arbitrage-trader/pkg/types"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
)

type Manager struct {
	maxDailyLoss     decimal.Decimal
	maxDrawdown      decimal.Decimal
	minProfitTarget  decimal.Decimal
	emergencyStopPct decimal.Decimal
}

func NewManager() *Manager {
	return &Manager{
		maxDailyLoss:     decimal.NewFromFloat(0.05), // 5% 最大每日损失
		maxDrawdown:      decimal.NewFromFloat(0.10), // 10% 最大回撤, 最大回撤是指账户净值从高点下跌的最大幅度
		minProfitTarget:  decimal.NewFromFloat(0.01), // 1% 最小利润目标
		emergencyStopPct: decimal.NewFromFloat(0.15), // 15% 紧急止损百分比
	}
}

// 计算风险参数,如止损,止盈,最大回撤,每日目标
func (rm *Manager) CalculateRiskParameters(
	gridConfig *types.GridParam,
	hedgePosition *types.HedgePosition,
	currentPrice decimal.Decimal,
) (*types.RiskManagement, error) {

	if gridConfig == nil {
		return nil, fmt.Errorf("grid configuration is required")
	}

	// 计算止损水平
	stopLoss := rm.calculateStopLoss(gridConfig, hedgePosition, currentPrice)

	// 计算止盈水平
	takeProfit := rm.calculateTakeProfit(gridConfig, currentPrice)

	// 计算最大回撤
	maxDrawdown := rm.calculateMaxDrawdown(gridConfig.Investment)

	// 设置每日目标 (网格配置中的预期回报)
	dailyTarget := gridConfig.ExpectedReturn

	riskMgmt := &types.RiskManagement{
		StopLoss:    stopLoss,
		TakeProfit:  takeProfit,
		MaxDrawdown: maxDrawdown,
		DailyTarget: dailyTarget,
	}

	// 验证风险参数
	if err := rm.validateRiskParameters(riskMgmt, currentPrice); err != nil {
		return nil, err
	}

	return riskMgmt, nil
}

// 计算止损水平
func (rm *Manager) calculateStopLoss(
	gridConfig *types.GridParam,
	hedgePosition *types.HedgePosition,
	currentPrice decimal.Decimal,
) decimal.Decimal {

	// 基础止损: 投资金额的百分比
	baseStopLoss := gridConfig.Investment.Mul(rm.maxDailyLoss)

	// 根据价格范围调整 - 止损应该在网格范围之外
	priceRange := gridConfig.PriceRange
	rangeBuffer := priceRange.UpperBound.Sub(priceRange.LowerBound).Mul(decimal.NewFromFloat(0.1)) // 10% buffer

	// 计算价格止损水平
	lowerStopPrice := priceRange.LowerBound.Sub(rangeBuffer)
	upperStopPrice := priceRange.UpperBound.Add(rangeBuffer)

	// 计算潜在损失
	midPrice := priceRange.LowerBound.Add(priceRange.UpperBound).Div(decimal.NewFromInt(2))

	var priceBasedStopLoss decimal.Decimal
	if currentPrice.LessThan(midPrice) {
		// 如果当前价格在下半部分, 重点保护下行
		priceMove := currentPrice.Sub(lowerStopPrice)
		priceBasedStopLoss = priceMove.Div(currentPrice).Mul(gridConfig.Investment)
	} else {
		// 如果当前价格在上半部分, 重点保护上行
		priceMove := upperStopPrice.Sub(currentPrice)
		priceBasedStopLoss = priceMove.Div(currentPrice).Mul(gridConfig.Investment)
	}

	// 使用更保守的止损水平
	return decimal.Min(baseStopLoss, priceBasedStopLoss.Abs())
}

// 计算止盈水平
func (rm *Manager) calculateTakeProfit(gridConfig *types.GridParam, currentPrice decimal.Decimal) decimal.Decimal {
	// 止盈应该等于网格配置中的预期回报
	expectedReturn := gridConfig.ExpectedReturn

	// 添加一个小缓冲 (10%) 以确保我们捕获利润
	takeProfitBuffer := expectedReturn.Mul(decimal.NewFromFloat(0.1))

	return expectedReturn.Add(takeProfitBuffer)
}

// 计算最大回撤
func (rm *Manager) calculateMaxDrawdown(investment decimal.Decimal) decimal.Decimal {
	return investment.Mul(rm.maxDrawdown)
}

// 监控实时风险指标
func (rm *Manager) MonitorRisk(
	session *types.TradingSession,
	currentPositions []types.Position,
	riskParams *types.RiskManagement,
) (*RiskStatus, error) {

	if session == nil || riskParams == nil {
		return nil, fmt.Errorf("session and risk parameters are required")
	}

	// 计算当前盈亏
	currentPnL := session.CurrentBalance.Sub(session.InitialBalance)

	// 计算回撤
	highWaterMark := session.InitialBalance.Add(session.RealizedPnL)
	if currentPnL.GreaterThan(decimal.Zero) {
		highWaterMark = decimal.Max(highWaterMark, session.CurrentBalance)
	}

	currentDrawdown := highWaterMark.Sub(session.CurrentBalance)

	// 检查风险条件
	riskStatus := &RiskStatus{
		CurrentPnL:      currentPnL,
		CurrentDrawdown: currentDrawdown,
		DailyPnL:        currentPnL, // Simplified - in practice track since session start
		RiskLevel:       rm.assessRiskLevel(currentPnL, currentDrawdown, riskParams),
		Alerts:          make([]string, 0),
	}

	// 生成警报
	rm.generateRiskAlerts(riskStatus, riskParams)

	return riskStatus, nil
}

// RiskStatus represents current risk status
type RiskStatus struct {
	CurrentPnL      decimal.Decimal `json:"current_pnl"`
	CurrentDrawdown decimal.Decimal `json:"current_drawdown"`
	DailyPnL        decimal.Decimal `json:"daily_pnl"`
	RiskLevel       string          `json:"risk_level"`
	Alerts          []string        `json:"alerts"`
	ShouldStop      bool            `json:"should_stop"`
	StopReason      string          `json:"stop_reason,omitempty"`
}

// 评估风险水平
func (rm *Manager) assessRiskLevel(currentPnL, currentDrawdown decimal.Decimal, riskParams *types.RiskManagement) string {
	// 检查止损是否触发
	if currentPnL.LessThan(riskParams.StopLoss.Neg()) {
		return "CRITICAL"
	}

	// 检查最大回撤是否超过
	if currentDrawdown.GreaterThan(riskParams.MaxDrawdown) {
		return "CRITICAL"
	}

	// 检查是否接近限制
	stopLossRatio := currentPnL.Abs().Div(riskParams.StopLoss)
	drawdownRatio := currentDrawdown.Div(riskParams.MaxDrawdown)

	if stopLossRatio.GreaterThan(decimal.NewFromFloat(0.8)) || drawdownRatio.GreaterThan(decimal.NewFromFloat(0.8)) {
		return "HIGH"
	}

	if stopLossRatio.GreaterThan(decimal.NewFromFloat(0.5)) || drawdownRatio.GreaterThan(decimal.NewFromFloat(0.5)) {
		return "MEDIUM"
	}

	return "LOW"
}

// 生成风险警报
func (rm *Manager) generateRiskAlerts(status *RiskStatus, riskParams *types.RiskManagement) {
	// 止损警报
	if status.CurrentPnL.LessThan(riskParams.StopLoss.Neg()) {
		status.Alerts = append(status.Alerts, "STOP LOSS TRIGGERED")
		status.ShouldStop = true
		status.StopReason = "Stop loss triggered"
	}

	// 止盈警报
	if status.CurrentPnL.GreaterThan(riskParams.TakeProfit) {
		status.Alerts = append(status.Alerts, "TAKE PROFIT TARGET REACHED")
		status.ShouldStop = true
		status.StopReason = "Take profit target reached"
	}

	// 最大回撤警报
	if status.CurrentDrawdown.GreaterThan(riskParams.MaxDrawdown) {
		status.Alerts = append(status.Alerts, "MAXIMUM DRAWDOWN EXCEEDED")
		status.ShouldStop = true
		status.StopReason = "Maximum drawdown exceeded"
	}

	// 警告警报
	stopLossWarning := riskParams.StopLoss.Mul(decimal.NewFromFloat(0.8))
	if status.CurrentPnL.LessThan(stopLossWarning.Neg()) && !status.ShouldStop {
		status.Alerts = append(status.Alerts, "Approaching stop loss level")
	}

	drawdownWarning := riskParams.MaxDrawdown.Mul(decimal.NewFromFloat(0.8))
	if status.CurrentDrawdown.GreaterThan(drawdownWarning) && !status.ShouldStop {
		status.Alerts = append(status.Alerts, "Approaching maximum drawdown")
	}
}

// 验证风险参数
func (rm *Manager) validateRiskParameters(riskParams *types.RiskManagement, currentPrice decimal.Decimal) error {
	// 检查止损是否合理
	if riskParams.StopLoss.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("stop loss must be positive")
	}

	// 检查止盈是否合理
	if riskParams.TakeProfit.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("take profit must be positive")
	}

	// 检查止盈是否大于最小值
	if riskParams.TakeProfit.LessThan(riskParams.StopLoss.Mul(decimal.NewFromFloat(0.5))) {
		return fmt.Errorf("take profit should be at least 50%% of stop loss for reasonable risk/reward")
	}

	return nil
}

// 计算仓位大小
func (rm *Manager) CalculatePositionSize(
	accountBalance decimal.Decimal,
	riskPerTrade decimal.Decimal,
	entryPrice decimal.Decimal,
	stopLossPrice decimal.Decimal,
) (decimal.Decimal, error) {

	if accountBalance.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero, fmt.Errorf("account balance must be positive")
	}

	if riskPerTrade.LessThanOrEqual(decimal.Zero) || riskPerTrade.GreaterThan(decimal.NewFromFloat(0.1)) {
		return decimal.Zero, fmt.Errorf("risk per trade must be between 0 and 10%%")
	}

	// 计算风险金额
	riskAmount := accountBalance.Mul(riskPerTrade)

	// 计算每单位价格风险
	priceRisk := entryPrice.Sub(stopLossPrice).Abs()
	if priceRisk.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero, fmt.Errorf("stop loss price must be different from entry price")
	}

	// 计算仓位大小
	positionSize := riskAmount.Div(priceRisk)

	return positionSize, nil
}

// 检查紧急止损条件
func (rm *Manager) CheckEmergencyStop(session *types.TradingSession) (bool, string) {
	if session == nil {
		return false, ""
	}

	// 检查会话是否运行时间过长 (24小时最大)
	sessionDuration := time.Since(session.StartTime)
	if sessionDuration > 24*time.Hour {
		return true, "Maximum session duration exceeded"
	}

	// 检查紧急止损百分比
	currentPnL := session.CurrentBalance.Sub(session.InitialBalance)
	emergencyLoss := session.InitialBalance.Mul(rm.emergencyStopPct)

	if currentPnL.LessThan(emergencyLoss.Neg()) {
		return true, "Emergency stop loss triggered"
	}

	return false, ""
}
