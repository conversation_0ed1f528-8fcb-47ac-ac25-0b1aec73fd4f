# 编译后的二进制文件
/main
/trader
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件，用go test -c构建
*.test

# 输出的二进制文件（go install）
*.out

# Go依赖管理
/vendor/

# Go工作区文件
go.work

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 配置文件（包含敏感信息）
config.json
*.env
.env.*

# 临时文件
*.tmp
*.temp

# 覆盖率报告
*.out
coverage.html

# 性能分析文件
*.prof

# 备份文件
*.bak
*.backup

# 本地开发工具
.air.toml