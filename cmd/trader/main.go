package main

import (
	"crypto-arbitrage-trader/internal/trader"
	"crypto-arbitrage-trader/pkg/config"
	"crypto-arbitrage-trader/pkg/utils/log"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/shopspring/decimal"
)

func main() {
	// Parse command line flags
	var (
		configFile = flag.String("config", "config.json", "Configuration file path")
		symbol     = flag.String("symbol", "", "Trading symbol (overrides config)")
		investment = flag.String("investment", "", "Investment amount (overrides config)")
		target     = flag.String("target", "", "Expected return (overrides config)")
		dryRun     = flag.Bool("dry-run", false, "Dry run mode (no actual trading)")
		template   = flag.Bool("template", false, "Generate config template")
	)
	flag.Parse()

	// Handle template generation
	if *template {
		fmt.Println("Configuration template:")
		fmt.Println(config.GetConfigTemplate())
		return
	}

	// Load and override configuration
	cfg, err := loadAndOverrideConfig(*configFile, *symbol, *investment, *target)
	if err != nil {
		fmt.Printf("Configuration error: %v\n", err)
		os.Exit(1)
	}

	// 初始化全局日志器
	log.InitGlobalLogger(cfg.Logging.Level, cfg.Logging.Format, cfg.Logging.File)

	// Initialize and start trader
	arbitrageTrader, err := trader.New(cfg, *dryRun)
	if err != nil {
		log.Errorf("Failed to initialize trader: %v", err)
		os.Exit(1)
	}

	if err := arbitrageTrader.Start(); err != nil {
		log.Errorf("Failed to start trader: %v", err)
		os.Exit(1)
	}

	// 使用全局日志器记录启动信息
	log.Info("Arbitrage trader started successfully")
	log.Infof("Trading %s with investment %s, target return %s",
		cfg.Trading.Symbol, cfg.Trading.Investment.String(), cfg.Trading.ExpectedDailyReturn.String())

	if *dryRun {
		log.Info("Running in DRY RUN mode - no actual trades will be executed")
	}

	// Wait for shutdown signal
	waitForShutdown(arbitrageTrader)
}

func loadAndOverrideConfig(configFile, symbol, investment, target string) (*config.Config, error) {
	cfg, err := config.LoadConfigWithDefaults(configFile)
	if err != nil {
		return nil, err
	}

	// Override with command line flags
	if symbol != "" {
		cfg.Trading.Symbol = symbol
	}
	if investment != "" {
		if val, err := decimal.NewFromString(investment); err == nil {
			cfg.Trading.Investment = val
		} else {
			return nil, fmt.Errorf("invalid investment amount: %s", investment)
		}
	}
	if target != "" {
		if val, err := decimal.NewFromString(target); err == nil {
			cfg.Trading.ExpectedDailyReturn = val
		} else {
			return nil, fmt.Errorf("invalid target return: %s", target)
		}
	}

	return cfg, nil
}

func waitForShutdown(arbitrageTrader *trader.ArbitrageTrader) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	<-sigChan
	log.Info("Received shutdown signal")

	if err := arbitrageTrader.Stop(); err != nil {
		log.Errorf("Error during shutdown: %v", err)
	}

	log.Info("Arbitrage trader stopped")
}
