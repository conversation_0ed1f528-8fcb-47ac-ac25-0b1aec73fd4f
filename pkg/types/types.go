package types

import (
	"time"

	"github.com/shopspring/decimal"
)

// Market represents a trading market
type Market struct {
	Symbol       string          `json:"symbol"`
	BaseAsset    string          `json:"base_asset"`
	QuoteAsset   string          `json:"quote_asset"`
	CurrentPrice decimal.Decimal `json:"current_price"`
	Volume24h    decimal.Decimal `json:"volume_24h"`
	PriceChange  decimal.Decimal `json:"price_change"`
}

// 价格范围表示操作建议的价格范围
type PriceRange struct {
	LowerBound decimal.Decimal `json:"lower_bound"` // 下限
	UpperBound decimal.Decimal `json:"upper_bound"` // 上限
	Confidence float64         `json:"confidence"`  // 置信度
	Reasoning  string          `json:"reasoning"`   // 理由
}

// GridParam 表示网格交易配置
type GridParam struct {
	Symbol         string          `json:"symbol"`
	Investment     decimal.Decimal `json:"investment"`      // 投资金额
	ExpectedReturn decimal.Decimal `json:"expected_return"` // 预期收益
	PriceRange     PriceRange      `json:"price_range"`     // 价格范围
	GridNum        int             `json:"grid_num"`        // 网格数量
	GridSpacing    decimal.Decimal `json:"grid_spacing"`    // 网格间距
	OrderSize      decimal.Decimal `json:"order_size"`      // 订单大小
	ProfitPerGrid  decimal.Decimal `json:"profit_per_grid"`
}

// HedgePosition represents hedge position calculation
type HedgePosition struct {
	Direction     string          `json:"direction"` // "long" or "short"
	Size          decimal.Decimal `json:"size"`
	EntryPrice    decimal.Decimal `json:"entry_price"`
	HedgeRatio    decimal.Decimal `json:"hedge_ratio"`
	CollateralReq decimal.Decimal `json:"collateral_required"`
}

// RiskManagement represents risk control parameters
type RiskManagement struct {
	StopLoss    decimal.Decimal `json:"stop_loss"`
	TakeProfit  decimal.Decimal `json:"take_profit"`
	MaxDrawdown decimal.Decimal `json:"max_drawdown"`
	DailyTarget decimal.Decimal `json:"daily_target"`
}

// Order represents a trading order
type Order struct {
	ID        string          `json:"id"`
	Symbol    string          `json:"symbol"`
	Side      string          `json:"side"` // "buy" or "sell"
	Type      string          `json:"type"` // "limit", "market"
	Quantity  decimal.Decimal `json:"quantity"`
	Price     decimal.Decimal `json:"price"`
	Status    string          `json:"status"`
	CreatedAt time.Time       `json:"created_at"`
	FilledQty decimal.Decimal `json:"filled_qty"`
	RemainQty decimal.Decimal `json:"remain_qty"`
}

// Position represents a current position
type Position struct {
	Symbol        string          `json:"symbol"`
	Side          string          `json:"side"`
	Size          decimal.Decimal `json:"size"`
	EntryPrice    decimal.Decimal `json:"entry_price"`
	MarkPrice     decimal.Decimal `json:"mark_price"`
	UnrealizedPnL decimal.Decimal `json:"unrealized_pnl"`
	MarginUsed    decimal.Decimal `json:"margin_used"`
}

// TradingSession represents a daily trading session
type TradingSession struct {
	ID             string          `json:"id"`
	Symbol         string          `json:"symbol"`
	StartTime      time.Time       `json:"start_time"`
	EndTime        *time.Time      `json:"end_time,omitempty"`
	Status         string          `json:"status"` // "active", "completed", "stopped"
	InitialBalance decimal.Decimal `json:"initial_balance"`
	CurrentBalance decimal.Decimal `json:"current_balance"`
	RealizedPnL    decimal.Decimal `json:"realized_pnl"`
	DailyTarget    decimal.Decimal `json:"daily_target"`
	StopReason     string          `json:"stop_reason,omitempty"`
}
