package log

import (
	"sync"

	"github.com/sirupsen/logrus"
)

// HybridLogger 混合日志器，支持全局和实例日志
type HybridLogger struct {
	globalLogger   *logrus.Logger
	instanceLogger *logrus.Logger
	useGlobal      bool
	mu             sync.RWMutex
}

// NewHybridLogger 创建混合日志器
func NewHybridLogger(useGlobal bool) *HybridLogger {
	hl := &HybridLogger{
		useGlobal: useGlobal,
	}

	if useGlobal {
		hl.globalLogger = GetGlobalLogger()
	}

	return hl
}

// SetInstanceLogger 设置实例日志器
func (hl *HybridLogger) SetInstanceLogger(logger *logrus.Logger) {
	hl.mu.Lock()
	defer hl.mu.Unlock()
	hl.instanceLogger = logger
}

// getLogger 获取当前使用的日志器
func (hl *HybridLogger) getLogger() *logrus.Logger {
	hl.mu.RLock()
	defer hl.mu.RUnlock()

	if hl.useGlobal {
		return hl.globalLogger
	}
	return hl.instanceLogger
}

// 实现 logrus.Logger 的接口方法
func (hl *HybridLogger) Info(args ...interface{}) {
	hl.getLogger().Info(args...)
}

func (hl *HybridLogger) Infof(format string, args ...interface{}) {
	hl.getLogger().Infof(format, args...)
}

func (hl *HybridLogger) Debug(args ...interface{}) {
	hl.getLogger().Debug(args...)
}

func (hl *HybridLogger) Debugf(format string, args ...interface{}) {
	hl.getLogger().Debugf(format, args...)
}

func (hl *HybridLogger) Warn(args ...interface{}) {
	hl.getLogger().Warn(args...)
}

func (hl *HybridLogger) Warnf(format string, args ...interface{}) {
	hl.getLogger().Warnf(format, args...)
}

func (hl *HybridLogger) Error(args ...interface{}) {
	hl.getLogger().Error(args...)
}

func (hl *HybridLogger) Errorf(format string, args ...interface{}) {
	hl.getLogger().Errorf(format, args...)
}

func (hl *HybridLogger) Fatal(args ...interface{}) {
	hl.getLogger().Fatal(args...)
}

func (hl *HybridLogger) Fatalf(format string, args ...interface{}) {
	hl.getLogger().Fatalf(format, args...)
}

func (hl *HybridLogger) WithField(key string, value interface{}) *logrus.Entry {
	return hl.getLogger().WithField(key, value)
}

func (hl *HybridLogger) WithFields(fields logrus.Fields) *logrus.Entry {
	return hl.getLogger().WithFields(fields)
}

func (hl *HybridLogger) WithError(err error) *logrus.Entry {
	return hl.getLogger().WithError(err)
}

// SwitchToGlobal 切换到全局日志
func (hl *HybridLogger) SwitchToGlobal() {
	hl.mu.Lock()
	defer hl.mu.Unlock()
	hl.useGlobal = true
}

// SwitchToInstance 切换到实例日志
func (hl *HybridLogger) SwitchToInstance() {
	hl.mu.Lock()
	defer hl.mu.Unlock()
	hl.useGlobal = false
}

// IsUsingGlobal 检查是否使用全局日志
func (hl *HybridLogger) IsUsingGlobal() bool {
	hl.mu.RLock()
	defer hl.mu.RUnlock()
	return hl.useGlobal
}
