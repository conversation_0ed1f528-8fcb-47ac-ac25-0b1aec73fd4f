package log

import (
	"fmt"
	"path"

	"github.com/sirupsen/logrus"
)

// CustomTextFormatter 自定义日志格式化器,显示短文件路径和行号
type CustomTextFormatter struct {
	FullTimestamp bool
}

func (f *CustomTextFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	timestamp := entry.Time.Format("2006-01-02T15:04:05-07:00")

	// 提取短文件路径和行号
	var caller string
	if entry.HasCaller() {
		filename := path.Base(entry.Caller.File)
		caller = fmt.Sprintf("[%s:%d] ", filename, entry.Caller.Line)
	}

	// 格式化日志条目
	msg := fmt.Sprintf("time=\"%s\" %slevel=%s msg=\"%s\"",
		timestamp, caller, entry.Level, entry.Message)

	// 如果有字段, 添加字段
	for key, value := range entry.Data {
		msg += fmt.Sprintf(" %s=%v", key, value)
	}

	return []byte(msg + "\n"), nil
}
