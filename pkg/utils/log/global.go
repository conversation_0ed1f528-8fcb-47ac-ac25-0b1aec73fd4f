package log

import (
	"os"
	"sync"

	"github.com/sirupsen/logrus"
)

var (
	globalLogger *logrus.Logger
	once         sync.Once
)

// InitGlobalLogger 初始化全局日志器
func InitGlobalLogger(level string, format string, file string) {
	once.Do(func() {
		globalLogger = logrus.New()

		// 设置日志级别
		if parsedLevel, err := logrus.ParseLevel(level); err == nil {
			globalLogger.SetLevel(parsedLevel)
		}

		// 设置格式化器
		if format == "json" {
			globalLogger.SetFormatter(&logrus.JSONFormatter{})
		} else {
			globalLogger.SetFormatter(&CustomTextFormatter{
				FullTimestamp: true,
			})
		}

		// 启用调用者报告
		globalLogger.SetReportCaller(true)

		// 设置输出文件
		if file != "" {
			if f, err := os.OpenFile(file, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
				globalLogger.SetOutput(f)
			}
		}
	})
}

// GetGlobalLogger 获取全局日志器
func GetGlobalLogger() *logrus.Logger {
	if globalLogger == nil {
		// 使用默认配置初始化
		InitGlobalLogger("info", "text", "")
	}
	return globalLogger
}

// 提供便捷的全局日志函数
func Info(args ...interface{}) {
	GetGlobalLogger().Info(args...)
}

func Infof(format string, args ...interface{}) {
	GetGlobalLogger().Infof(format, args...)
}

func Debug(args ...interface{}) {
	GetGlobalLogger().Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	GetGlobalLogger().Debugf(format, args...)
}

func Warn(args ...interface{}) {
	GetGlobalLogger().Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	GetGlobalLogger().Warnf(format, args...)
}

func Error(args ...interface{}) {
	GetGlobalLogger().Error(args...)
}

func Errorf(format string, args ...interface{}) {
	GetGlobalLogger().Errorf(format, args...)
}

func Fatal(args ...interface{}) {
	GetGlobalLogger().Fatal(args...)
}

func Fatalf(format string, args ...interface{}) {
	GetGlobalLogger().Fatalf(format, args...)
}

// WithField 添加字段
func WithField(key string, value interface{}) *logrus.Entry {
	return GetGlobalLogger().WithField(key, value)
}

// WithFields 添加多个字段
func WithFields(fields logrus.Fields) *logrus.Entry {
	return GetGlobalLogger().WithFields(fields)
}

// WithError 添加错误
func WithError(err error) *logrus.Entry {
	return GetGlobalLogger().WithError(err)
}
