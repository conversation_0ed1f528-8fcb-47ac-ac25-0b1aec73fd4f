package config

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/shopspring/decimal"
)

type Config struct {
	Exchange ExchangeConfig `json:"exchange"`
	Trading  TradingConfig  `json:"trading"`
	Risk     RiskConfig     `json:"risk"`
	Logging  LoggingConfig  `json:"logging"`
}

type ExchangeConfig struct {
	Name      string            `json:"name"` // "binance" or "gate"
	APIKey    string            `json:"api_key"`
	SecretKey string            `json:"secret_key"`
	BaseURL   string            `json:"base_url,omitempty"`
	TestNet   bool              `json:"testnet"`
	Config    map[string]string `json:"config,omitempty"`
}

type TradingConfig struct {
	Symbol              string          `json:"symbol"`                // e.g., "BTCUSDT"
	Investment          decimal.Decimal `json:"investment"`            // 投资金额
	ExpectedDailyReturn decimal.Decimal `json:"expected_daily_return"` // 预期每日回报
	MinGridNum          int             `json:"min_grid_num"`
	MaxGridNum          int             `json:"max_grid_num"`
	DefaultHedgeRatio   decimal.Decimal `json:"default_hedge_ratio"`
	SessionTimeout      int             `json:"session_timeout"` // 会话超时时间(小时)
	AutoRestart         bool            `json:"auto_restart"`    // 自动重启(达到目标后)
}

type RiskConfig struct {
	MaxDailyLoss         decimal.Decimal `json:"max_daily_loss"`         // 最大每日损失百分比
	MaxDrawdown          decimal.Decimal `json:"max_drawdown"`           // 最大回撤百分比
	StopLossMultiplier   decimal.Decimal `json:"stop_loss_multiplier"`   // 止损倍数
	TakeProfitMultiplier decimal.Decimal `json:"take_profit_multiplier"` // 止盈倍数
	EmergencyStopPct     decimal.Decimal `json:"emergency_stop_pct"`     // 紧急止损百分比
	MinProfitRate        decimal.Decimal `json:"min_profit_rate"`        // 每网格最小利润率
}

type LoggingConfig struct {
	Level   string `json:"level"`   // "debug", "info", "warn", "error"
	Format  string `json:"format"`  // "json" or "text"
	File    string `json:"file"`    // 日志文件路径(可选)
	Console bool   `json:"console"` // 启用控制台日志
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Exchange: ExchangeConfig{
			Name:    "binance",
			TestNet: true,
		},
		Trading: TradingConfig{
			Symbol:              "BTCUSDT",
			Investment:          decimal.NewFromInt(1000),
			ExpectedDailyReturn: decimal.NewFromInt(20),
			MinGridNum:          10,
			MaxGridNum:          30,
			DefaultHedgeRatio:   decimal.NewFromFloat(0.5),
			SessionTimeout:      24,
			AutoRestart:         false,
		},
		Risk: RiskConfig{
			MaxDailyLoss:         decimal.NewFromFloat(0.05),
			MaxDrawdown:          decimal.NewFromFloat(0.10),
			StopLossMultiplier:   decimal.NewFromFloat(1.0),
			TakeProfitMultiplier: decimal.NewFromFloat(1.1),
			EmergencyStopPct:     decimal.NewFromFloat(0.15),
			MinProfitRate:        decimal.NewFromFloat(0.002),
		},
		Logging: LoggingConfig{
			Level:   "info",
			Format:  "text",
			Console: true,
		},
	}
}

// LoadConfig loads configuration from file
func LoadConfig(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// Validate configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &config, nil
}

// LoadConfigWithDefaults loads config from file with default fallbacks
func LoadConfigWithDefaults(filename string) (*Config, error) {
	config := DefaultConfig()

	if filename != "" && fileExists(filename) {
		loadedConfig, err := LoadConfig(filename)
		if err != nil {
			return nil, err
		}
		config = loadedConfig
	}

	// Override with environment variables if set
	config.loadFromEnv()

	return config, nil
}

// SaveConfig saves configuration to file
func (c *Config) SaveConfig(filename string) error {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate exchange config
	if c.Exchange.Name == "" {
		return fmt.Errorf("exchange name is required")
	}

	if c.Exchange.APIKey == "" {
		return fmt.Errorf("exchange API key is required")
	}

	if c.Exchange.SecretKey == "" {
		return fmt.Errorf("exchange secret key is required")
	}

	// Validate trading config
	if c.Trading.Symbol == "" {
		return fmt.Errorf("trading symbol is required")
	}

	if c.Trading.Investment.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("investment amount must be positive")
	}

	if c.Trading.ExpectedDailyReturn.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("expected daily return must be positive")
	}

	if c.Trading.MinGridNum < 3 || c.Trading.MaxGridNum > 100 {
		return fmt.Errorf("grid num must be between 3 and 100")
	}

	if c.Trading.MinGridNum >= c.Trading.MaxGridNum {
		return fmt.Errorf("min grid num must be less than max grid num")
	}

	if c.Trading.DefaultHedgeRatio.LessThan(decimal.Zero) || c.Trading.DefaultHedgeRatio.GreaterThan(decimal.NewFromInt(1)) {
		return fmt.Errorf("hedge ratio must be between 0 and 1")
	}

	// Validate risk config
	if c.Risk.MaxDailyLoss.LessThan(decimal.Zero) || c.Risk.MaxDailyLoss.GreaterThan(decimal.NewFromFloat(0.5)) {
		return fmt.Errorf("max daily loss must be between 0 and 50%%")
	}

	if c.Risk.MaxDrawdown.LessThan(decimal.Zero) || c.Risk.MaxDrawdown.GreaterThan(decimal.NewFromFloat(0.5)) {
		return fmt.Errorf("max drawdown must be between 0 and 50%%")
	}

	// Validate logging config
	validLevels := map[string]bool{"debug": true, "info": true, "warn": true, "error": true}
	if !validLevels[c.Logging.Level] {
		return fmt.Errorf("invalid log level: %s", c.Logging.Level)
	}

	return nil
}

// loadFromEnv loads configuration from environment variables
func (c *Config) loadFromEnv() {
	if apiKey := os.Getenv("EXCHANGE_API_KEY"); apiKey != "" {
		c.Exchange.APIKey = apiKey
	}

	if secretKey := os.Getenv("EXCHANGE_SECRET_KEY"); secretKey != "" {
		c.Exchange.SecretKey = secretKey
	}

	if exchangeName := os.Getenv("EXCHANGE_NAME"); exchangeName != "" {
		c.Exchange.Name = exchangeName
	}

	if symbol := os.Getenv("TRADING_SYMBOL"); symbol != "" {
		c.Trading.Symbol = symbol
	}

	if investment := os.Getenv("TRADING_INVESTMENT"); investment != "" {
		if val, err := decimal.NewFromString(investment); err == nil {
			c.Trading.Investment = val
		}
	}

	if expectedReturn := os.Getenv("TRADING_EXPECTED_RETURN"); expectedReturn != "" {
		if val, err := decimal.NewFromString(expectedReturn); err == nil {
			c.Trading.ExpectedDailyReturn = val
		}
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		c.Logging.Level = logLevel
	}
}

// GetExchangeConfig returns exchange config as map for factory
func (c *Config) GetExchangeConfig() map[string]string {
	config := map[string]string{
		"api_key":    c.Exchange.APIKey,
		"secret_key": c.Exchange.SecretKey,
	}

	if c.Exchange.BaseURL != "" {
		config["base_url"] = c.Exchange.BaseURL
	}

	// Add additional config
	for k, v := range c.Exchange.Config {
		config[k] = v
	}

	return config
}

// fileExists checks if file exists
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// GetTradingPair parses trading symbol into base and quote assets
func (c *Config) GetTradingPair() (string, string, error) {
	symbol := c.Trading.Symbol

	// Common quote assets for crypto
	quoteAssets := []string{"USDT", "BUSD", "USDC", "BTC", "ETH", "BNB"}

	for _, quote := range quoteAssets {
		if len(symbol) > len(quote) && symbol[len(symbol)-len(quote):] == quote {
			base := symbol[:len(symbol)-len(quote)]
			return base, quote, nil
		}
	}

	return "", "", fmt.Errorf("unable to parse trading pair: %s", symbol)
}

// GetConfigTemplate returns a template configuration for reference
func GetConfigTemplate() string {
	config := DefaultConfig()
	config.Exchange.APIKey = "your_api_key_here"
	config.Exchange.SecretKey = "your_secret_key_here"

	data, _ := json.MarshalIndent(config, "", "  ")
	return string(data)
}
