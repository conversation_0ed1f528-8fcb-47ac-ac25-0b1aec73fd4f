# 加密货币套利交易系统

一个用Go语言开发的加密货币套利交易系统，主要特点包括网格交易、对冲仓位管理、智能风险控制等功能。

## 核心特性

### 🎯 主要功能
1. **市场分析**: 自动分析市场并给出建议的操作价格区间
2. **网格交易**: 根据投资额和预期收益自动计算网格参数
3. **对冲仓位**: 智能计算对冲策略以降低风险
4. **风险管理**: 自动止损止盈，支持日级别交易控制
5. **多交易所**: 支持币安(Binance)和Gate.io等主流交易所

### 🔧 技术特点
- 使用decimal库确保高精度计算
- 实时订单监控和自动补单
- 完整的日志记录和会话管理
- 支持干运行模式测试策略
- 灵活的配置管理

## 系统架构

```
crypto-arbitrage-trader/
├── cmd/trader/           # 主程序入口
├── internal/            # 内部模块
│   ├── trader/         # 核心交易器
│   ├── market/         # 市场分析模块
│   ├── grid/           # 网格交易计算引擎
│   ├── hedge/          # 对冲仓位计算
│   ├── risk/           # 风险管理模块
│   ├── exchange/       # 交易所API接口层
│   └── order/          # 订单管理和仓位控制
├── pkg/                # 公共包
│   ├── types/          # 数据类型定义
│   ├── config/         # 配置管理
│   └── utils/          # 工具函数
└── config.example.json  # 配置文件示例
```

## 快速开始

### 1. 安装依赖

```bash
go mod download
```

### 2. 配置设置

复制配置文件示例并修改：

```bash
cp config.example.json config.json
```

编辑 `config.json` 文件，填入你的交易所API密钥：

```json
{
  "exchange": {
    "name": "binance",
    "api_key": "你的API密钥",
    "secret_key": "你的私钥",
    "testnet": true
  },
  "trading": {
    "symbol": "BTCUSDT",
    "investment": "1000",
    "expected_daily_return": "20"
  }
}
```

### 3. 运行程序

**干运行模式(推荐先测试):**
```bash
go run cmd/trader/main.go -config config.json -dry-run
```

**实际交易模式:**
```bash
go run cmd/trader/main.go -config config.json
```

**命令行参数覆盖:**
```bash
go run cmd/trader/main.go -symbol ETHUSDT -investment 500 -target 15
```

## 详细运行逻辑举例

### 🎯 完整交易流程演示

以下是一个完整的交易流程示例，展示系统如何分析市场、计算参数并执行交易策略：

#### **场景设置**
- 交易对：ETH/USDT
- 投资金额：200 USDT
- 目标收益：5 USDT/天
- 当前ETH价格：3793.64 USDT

#### **第一步：市场分析**
```bash
time="2025-07-30T23:10:00+08:00" [trader.go:205] level=info msg="Current market price: 3793.64"
```

系统首先获取当前市场数据，包括：
- 当前价格：3793.64 USDT
- 24小时交易量
- 价格变化幅度

#### **第二步：价格区间建议**
```bash
time="2025-07-30T23:10:00+08:00" [trader.go:213] level=info msg="Suggested price range: 3717.77 - 3800.00 (confidence: 85.00%)"
```

**分析过程**：
1. **波动率分析**：基于24小时价格变化计算波动率
2. **支撑阻力分析**：识别心理价位（如3800整数关口）
3. **综合评估**：结合两种分析方法，给出保守范围
4. **置信度计算**：基于区间大小和市场条件评估可靠性

**结果解读**：
- 建议操作区间：3717.77 - 3800.00（约2.2%波动范围）
- 置信度：85%（较高可靠性）

#### **第三步：网格参数计算**
```bash
time="2025-07-30T23:10:00+08:00" [trader.go:227] level=info msg="Grid configuration: 5 levels, spacing: 20.56, order size: 28"
```

**计算逻辑**：
1. **网格层数**：根据投资额和目标收益计算最优层数
   - 投资金额：200 USDT
   - 目标收益：5 USDT → 收益率2.5%
   - 计算结果：5层网格（平衡执行概率和收益率）

2. **网格间距**：
   - 价格区间：3800 - 3717.77 = 82.23 USDT
   - 网格间距：82.23 ÷ (5-1) = 20.56 USDT
   - 间距占比：20.56 ÷ 3793.64 ≈ 0.54%

3. **订单大小**：
   - 可用资金：200 × 70% = 140 USDT（预留30%做对冲和风险缓冲）
   - 每格订单：140 ÷ 5 = 28 USDT等值

#### **第四步：对冲仓位设计**
```bash
time="2025-07-30T23:10:00+08:00" [trader.go:237] level=info msg="Hedge position: short 42, ratio: 0.3, collateral: 25359.26"
```

**对冲策略分析**：
1. **对冲方向**：Short（做空对冲）
   - 当前价格3793.64处于区间上半部分
   - 预期更多买入订单被执行，形成多头敞口
   - 采用做空对冲平衡风险

2. **对冲比例**：30%
   - 基于市场波动率（中等）设定
   - 平衡对冲成本和风险保护

3. **对冲规模**：42个单位
   - 总网格敞口：28 × 5 = 140 USDT等值
   - 对冲数量：140 × 0.3 ÷ 3793.64 ≈ 0.011 ETH

4. **保证金需求**：约25,359 USDT
   - 基于15%保证金率计算
   - **注意**：这个数值过高，实际应用中需要优化

#### **第五步：风险参数设置**
```bash
time="2025-07-30T23:10:00+08:00" [trader.go:247] level=info msg="Risk parameters - Stop Loss: 0.77, Take Profit: 5.5"
```

**风险控制机制**：
1. **止损位**：0.77 USDT（约0.38%的本金）
   - 基于最大日损失5%配置
   - 价格突破操作区间时触发

2. **止盈位**：5.5 USDT（目标收益 + 10%缓冲）
   - 达到目标收益后自动止盈
   - 避免贪婪导致的回撤

#### **第六步：订单部署**
```bash
time="2025-07-30T23:10:00+08:00" [trader.go:256] level=info msg="Generated 2 buy levels and 2 sell levels"
```

**网格订单分布**：
```
卖单 #2: 3800.00 USDT (28 USDT等值)  ← 阻力位
卖单 #1: 3779.44 USDT (28 USDT等值)
--------- 当前价格: 3793.64 ---------
买单 #1: 3758.88 USDT (28 USDT等值)
买单 #2: 3717.77 USDT (28 USDT等值)  ← 支撑位
```

### 📊 运行中的动态调整

#### **订单执行监控**
系统每30秒检查一次订单状态：
```bash
time="2025-07-30T23:12:30+08:00" [trader.go:295] level=info msg="Session PnL: 1.25, Risk Level: LOW"
time="2025-07-30T23:12:30+08:00" [trader.go:315] level=debug msg="Order summary: map[active_orders:4 filled_orders:1 total_orders:5]"
```

#### **风险预警系统**
当达到风险阈值时：
```bash
time="2025-07-30T23:15:00+08:00" [trader.go:303] level=warn msg="Approaching stop loss level"
time="2025-07-30T23:16:00+08:00" [trader.go:307] level=info msg="Stopping trading: Take profit target reached"
```

#### **自动补单机制**
当网格订单成交时：
```bash
time="2025-07-30T23:13:45+08:00" [manager.go:178] level=info msg="Order filled: buy 0.0074 ETHUSDT at 3758.88"
time="2025-07-30T23:13:45+08:00" [manager.go:195] level=info msg="Opposite grid order placed: sell_1234 at 3779.44"
```

### 🎛️ 参数调整策略

#### **根据市场条件调整**
1. **高波动市场**（>10%日波动）
   - 增加网格间距到1-2%
   - 提高对冲比例到50-70%
   - 缩小操作区间到3-5%

2. **低波动市场**（<5%日波动）
   - 减少网格间距到0.2-0.5%
   - 降低对冲比例到20-30%
   - 扩大操作区间到5-8%

#### **根据资金规模调整**
1. **小资金**（<1000 USDT）
   - 减少网格层数到5-10层
   - 选择高流动性交易对
   - 重点关注手续费优化

2. **大资金**（>10000 USDT）
   - 增加网格层数到20-50层
   - 分散到多个交易对
   - 加强对冲和风险管理

### 💡 实战优化建议

1. **首次运行建议**：
   ```bash
   # 小额测试
   ./trader -dry-run -investment 100 -target 2 -symbol ETHUSDT
   
   # 观察参数合理性，然后实盘小额验证
   ./trader -investment 100 -target 2 -symbol ETHUSDT
   ```

2. **参数调优顺序**：
   - 先确定合适的价格区间
   - 再优化网格层数和间距
   - 最后调整对冲比例和风险参数

3. **监控重点指标**：
   - 网格执行频率（理想：每层每天1-3次）
   - 对冲有效性（风险降低程度）
   - 手续费占比（应<收益的20%）

## 详细配置说明

### 交易所配置 (exchange)
- `name`: 交易所名称 ("binance" 或 "gate")
- `api_key`: API密钥
- `secret_key`: API私钥  
- `testnet`: 是否使用测试网
- `base_url`: 自定义API地址(可选)

### 交易配置 (trading)
- `symbol`: 交易对，如 "BTCUSDT"
- `investment`: 总投资金额(USDT)
- `expected_daily_return`: 每日预期收益(USDT)
- `min_grid_levels`: 最小网格层数
- `max_grid_levels`: 最大网格层数
- `default_hedge_ratio`: 默认对冲比例(0.0-1.0)
- `session_timeout`: 会话超时时间(小时)

### 风险配置 (risk)
- `max_daily_loss`: 最大日损失比例(0.05 = 5%)
- `max_drawdown`: 最大回撤比例(0.1 = 10%)
- `emergency_stop_pct`: 紧急停止比例(0.15 = 15%)
- `min_profit_rate`: 每网格最小利润率(0.002 = 0.2%)

## 工作流程

### 1. 市场分析阶段
系统会分析当前市场状况，包括：
- 当前价格和24小时波动
- 计算建议的操作价格区间
- 评估市场波动性和置信度

### 2. 参数计算阶段
根据用户设置自动计算：
- **网格参数**: 网格层数、间距、每格订单大小
- **对冲仓位**: 对冲方向、大小、所需保证金
- **风险参数**: 止损位、止盈位、最大回撤

### 3. 订单执行阶段
- 创建买入和卖出网格订单
- 建立对冲仓位(如果支持)
- 实时监控订单状态

### 4. 风险监控阶段
- 实时计算已实现盈亏
- 监控是否触及止损止盈条件
- 检查是否达到每日目标收益

## 使用示例

### 示例1: 基本网格交易
```bash
# 用1000 USDT在BTC上做网格交易，目标日收益20 USDT
go run cmd/trader/main.go -symbol BTCUSDT -investment 1000 -target 20
```

### 示例2: 高频小额交易
```bash
# 用500 USDT在ETH上做高频交易，目标日收益10 USDT
go run cmd/trader/main.go -symbol ETHUSDT -investment 500 -target 10
```

### 示例3: 测试模式
```bash
# 干运行模式测试策略
go run cmd/trader/main.go -dry-run -symbol ADAUSDT -investment 200 -target 5
```

## 风险提示

⚠️ **重要风险提示**:
1. 加密货币交易存在高风险，可能导致资金损失
2. 请在测试网或小额资金上先行测试
3. 确保理解所有参数的含义再进行实盘交易
4. 建议设置合理的止损止盈参数
5. 监控系统运行状态，及时处理异常情况

## 系统监控

### 日志监控
系统会输出详细的运行日志，包含简洁的文件名和行号信息：
```
time="2025-07-30T23:10:00+08:00" [main.go:55] level=info msg="Arbitrage trader started successfully"
time="2025-07-30T23:10:00+08:00" [main.go:56] level=info msg="Trading ETHUSDT with investment 200, target return 5"
time="2025-07-30T23:10:00+08:00" [main.go:60] level=info msg="Running in DRY RUN mode - no actual trades will be executed"
time="2025-07-30T23:10:00+08:00" [trader.go:205] level=info msg="Current market price: 3793.64"
time="2025-07-30T23:10:00+08:00" [trader.go:213] level=info msg="Suggested price range: 3717.77 - 3800.00 (confidence: 85.00%)"
time="2025-07-30T23:10:00+08:00" [trader.go:227] level=info msg="Grid configuration: 5 levels, spacing: 20.56, order size: 28"
```

### 会话总结
每次交易会话结束时会输出完整的统计报告：
```
=== Trading Session Summary ===
Session ID: session_1704110400
Symbol: BTCUSDT
Duration: 2h15m30s
Initial Balance: 1000.00
Final Balance: 1018.50
Realized PnL: 18.50
Return Rate: 1.85%
Target Return: 20.00
Stop Reason: Take profit target reached
Status: completed
================================
```

## 编译和部署

### 编译二进制文件
```bash
# 编译当前平台
go build -o trader cmd/trader/main.go

# 交叉编译Linux版本
GOOS=linux GOARCH=amd64 go build -o trader-linux cmd/trader/main.go

# 交叉编译Windows版本  
GOOS=windows GOARCH=amd64 go build -o trader.exe cmd/trader/main.go
```

### Docker部署
```bash
# 创建Dockerfile
cat > Dockerfile << EOF
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o trader cmd/trader/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/trader .
COPY config.json .
CMD ["./trader"]
EOF

# 构建和运行
docker build -t crypto-arbitrage-trader .
docker run -v $(pwd)/config.json:/root/config.json crypto-arbitrage-trader
```

## 开发和扩展

### 添加新的交易所
1. 在 `internal/exchange/` 中实现 `Exchange` 接口
2. 在 `ExchangeFactory` 中注册新交易所
3. 更新配置文件支持新交易所

### 添加新的策略
1. 在相应模块中实现新的计算逻辑
2. 更新配置结构支持新参数
3. 在主程序中集成新策略

### 测试
```bash
# 运行所有测试
go test ./...

# 运行特定模块测试
go test ./internal/grid/

# 运行基准测试
go test -bench=. ./internal/grid/
```

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 免责声明

本软件仅供学习和研究使用。使用本软件进行实际交易的风险由用户自行承担。作者不对使用本软件造成的任何损失承担责任。